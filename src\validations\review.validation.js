const Joi = require('joi');
const { Order} = require('../models');
const { exists, objectId } = require('./custom.validation');

// Individual validators
const order = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: Order, field: '_id', value });
    return value;
  });


// Schemas
const params = {
  params: Joi.object({
    reviewId: Joi.string().required().custom(objectId),
  }),
};

const create = {
  body: Joi.object({
    order_id: order,
    rating: Joi.number().min(1).max(5).required(),
    title: Joi.string().max(255).required(),
    experience: Joi.string().optional().allow('', null),
    image:Joi.string().optional().allow('', null),
  }),
};

const update = {
  params: Joi.object({
    reviewId: Joi.string().required().custom(objectId),
  }),
  body: Joi.object({
    rating: Joi.number().min(1).max(5),
    title: Joi.string().max(255),
    experience: Joi.string().optional().allow('', null),
    image:Joi.string().optional().allow('', null),
  }).min(1),
};

module.exports = {
  params,
  create,
  update,
};
