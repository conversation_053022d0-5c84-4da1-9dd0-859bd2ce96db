const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const MongooseDelete = require('mongoose-delete');

const customerSchema = mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    username: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    emailPermission: {
      type: Boolean,
      default: false,
    },
    photo: {
      type: String,
      default: null,
      trim: true,
    },
    bio: {
      type: String,
      maxlength: 500, // Limiting the length of the bio
      default: null,
      trim: true,
    },
    notificationPermission: {
      type: <PERSON>olean,
      default: false,
    },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number],
      required: false, // Changed from true to false
      validate: {
        validator: function(v) {
          // Only validate if coordinates are provided
          if (!v || v.length === 0) return true;
          return v.length === 2 &&
                 typeof v[0] === 'number' && // longitude
                 typeof v[1] === 'number';   // latitude
        },
        message: 'Invalid coordinates - must be [longitude, latitude] numbers'
      }
    }
  },
    address: {

      phone: { type: String },
      pincode: { type: String},
      locality: { type: String },
      address_line: { type: String },
      city: { type: String},
      state: { type: String },
      landmark: { type: String },
      required:false
    },


  },
  {
    timestamps: true,
  }
);

customerSchema.plugin(paginate);
customerSchema.plugin(MongooseDelete, { overrideMethods: 'all' });
customerSchema.plugin(toJSON);

/**
 * @typedef Customer
 */
const Customer = mongoose.model('customer', customerSchema);
module.exports = Customer;
