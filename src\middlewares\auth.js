const passport = require('passport');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { roleRights } = require('../config/roles');

// Import models at module level for better performance
const { Customer, Provider } = require('../models');

// Simple in-memory cache with TTL for user data
const userCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache

// Cache helper functions
const getCacheKey = (role, userId) => `${role}:${userId}`;

const getCachedUserData = (role, userId) => {
  const key = getCacheKey(role, userId);
  const cached = userCache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }
  return null;
};

const setCachedUserData = (role, userId, data) => {
  const key = getCacheKey(role, userId);
  userCache.set(key, {
    data,
    timestamp: Date.now()
  });

  // Clean up old cache entries periodically
  if (userCache.size > 1000) {
    const now = Date.now();
    for (const [k, v] of userCache.entries()) {
      if (now - v.timestamp > CACHE_TTL) {
        userCache.delete(k);
      }
    }
  }
};

const verifyCallback = (req, resolve, reject, requiredRights) => async (err, user, info) => {
  if (err || info || !user) {
    return reject(new ApiError(httpStatus.UNAUTHORIZED, 'Please authenticate'));
  }

  req.user = user;

  try {
    if (user.role === 'customer') {
      // Check cache first
      let customer = getCachedUserData('customer', user._id);
      if (!customer) {
        // Use lean query for better performance
        customer = await Customer.findOne({ user: user._id }).lean();
        if (customer) {
          setCachedUserData('customer', user._id, customer);
        }
      }
      req.customer = customer;
    } else if (user.role === 'provider') {
      // Check cache first
      let provider = getCachedUserData('provider', user._id);
      if (!provider) {
        // Use lean query with only necessary fields for better performance
        provider = await Provider.findOne({ user: user._id })
          .select('isApproved user name')
          .lean();
        if (provider) {
          setCachedUserData('provider', user._id, provider);
        }
      }

      provider = provider ?? {};

      // Only check approval status for non-profile routes
      if (!(req.path === '/profile' && (req.method === 'GET' || req.method === 'POST'))) {
        if (!provider.hasOwnProperty('isApproved')) {
          return reject(new ApiError(httpStatus.NOT_ACCEPTABLE, 'Please complete your profile'));
        } else if (provider.isApproved !== 'approved') {
          return reject(new ApiError(httpStatus.NOT_ACCEPTABLE, 'Please wait for admin approval, currently your status is, ' + provider.isApproved));
        }
      }
      req.provider = provider;
    }

    // Optimize rights checking
    if (requiredRights.length) {
      const userRights = roleRights.get(user.role);
      if (!userRights) {
        return reject(new ApiError(httpStatus.FORBIDDEN, 'Invalid user role'));
      }

      const hasRequiredRights = requiredRights.every((requiredRight) => userRights.includes(requiredRight));
      if (!hasRequiredRights && req.params.userId !== user.id) {
        return reject(new ApiError(httpStatus.FORBIDDEN, 'Forbidden'));
      }
    }

    resolve();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return reject(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Authentication error'));
  }
};

const auth =
  (...requiredRights) =>
  async (req, res, next) => {
    return new Promise((resolve, reject) => {
      passport.authenticate('jwt', { session: false }, verifyCallback(req, resolve, reject, requiredRights))(req, res, next);
    })
      .then(() => next())
      .catch((err) => next(err));
  };

module.exports = auth;
