const faker = require('faker');
const { Provider, Category, SubCategory, Product } = require('../src/models');

/**
 * Cannabis strain names and product data
 */
const cannabisStrains = [
  // Indica Strains
  { name: '<PERSON> Kush', type: 'Indica', thc: '18-22%', cbd: '0.1-0.3%', effects: ['Relaxation', 'Sleep', 'Pain Relief'] },
  { name: 'Granddaddy Purple', type: 'Indica', thc: '17-23%', cbd: '0.1-0.2%', effects: ['Euphoria', 'Relaxation', 'Sleep'] },
  { name: 'Northern Lights', type: 'Indica', thc: '16-21%', cbd: '0.1-0.2%', effects: ['Relaxation', 'Sleep', 'Stress Relief'] },
  { name: '<PERSON>sh', type: 'Indica', thc: '14-22%', cbd: '0.1-0.2%', effects: ['Relaxation', 'Sleep', 'Pain Relief'] },
  { name: 'Afghan Kush', type: 'Indica', thc: '16-20%', cbd: '0.1-0.3%', effects: ['Relaxation', 'Pain Relief', 'Sleep'] },
  
  // Sativa Strains
  { name: 'Sour Diesel', type: 'Sativa', thc: '19-25%', cbd: '0.1-0.2%', effects: ['Energy', 'Focus', 'Creativity'] },
  { name: 'Jack Herer', type: 'Sativa', thc: '18-23%', cbd: '0.1-0.2%', effects: ['Energy', 'Focus', 'Euphoria'] },
  { name: 'Green Crack', type: 'Sativa', thc: '15-25%', cbd: '0.1-0.2%', effects: ['Energy', 'Focus', 'Mood Enhancement'] },
  { name: 'Durban Poison', type: 'Sativa', thc: '15-25%', cbd: '0.1-0.2%', effects: ['Energy', 'Focus', 'Creativity'] },
  { name: 'Super Silver Haze', type: 'Sativa', thc: '18-23%', cbd: '0.1-0.2%', effects: ['Energy', 'Euphoria', 'Creativity'] },
  
  // Hybrid Strains
  { name: 'Blue Dream', type: 'Hybrid', thc: '17-24%', cbd: '0.1-0.2%', effects: ['Relaxation', 'Euphoria', 'Creativity'] },
  { name: 'Girl Scout Cookies', type: 'Hybrid', thc: '19-28%', cbd: '0.1-0.2%', effects: ['Euphoria', 'Relaxation', 'Appetite'] },
  { name: 'OG Kush', type: 'Hybrid', thc: '19-26%', cbd: '0.1-0.3%', effects: ['Euphoria', 'Relaxation', 'Stress Relief'] },
  { name: 'White Widow', type: 'Hybrid', thc: '18-25%', cbd: '0.1-0.2%', effects: ['Euphoria', 'Energy', 'Creativity'] },
  { name: 'Pineapple Express', type: 'Hybrid', thc: '16-24%', cbd: '0.1-0.2%', effects: ['Energy', 'Euphoria', 'Focus'] },
  
  // High CBD Strains
  { name: 'Charlotte\'s Web', type: 'CBD', thc: '0.3%', cbd: '13-20%', effects: ['Pain Relief', 'Anxiety Relief', 'Anti-inflammatory'] },
  { name: 'ACDC', type: 'CBD', thc: '1-6%', cbd: '14-20%', effects: ['Pain Relief', 'Anxiety Relief', 'Focus'] },
  { name: 'Harlequin', type: 'CBD', thc: '5-10%', cbd: '10-15%', effects: ['Pain Relief', 'Anxiety Relief', 'Clear-headed'] },
  { name: 'Cannatonic', type: 'CBD', thc: '6-17%', cbd: '6-17%', effects: ['Relaxation', 'Pain Relief', 'Anxiety Relief'] },
  { name: 'Ringo\'s Gift', type: 'CBD', thc: '1%', cbd: '13-24%', effects: ['Pain Relief', 'Anxiety Relief', 'Anti-inflammatory'] },
];

/**
 * Edible products
 */
const edibleProducts = [
  { name: 'Mixed Berry Gummies', category: 'Edibles', subCategory: 'Gummies', thc: '10mg per piece' },
  { name: 'Dark Chocolate Bar', category: 'Edibles', subCategory: 'Chocolates', thc: '100mg total' },
  { name: 'Sour Apple Gummies', category: 'Edibles', subCategory: 'Gummies', thc: '5mg per piece' },
  { name: 'Peanut Butter Cookies', category: 'Edibles', subCategory: 'Cookies', thc: '25mg per cookie' },
  { name: 'Lemonade Drink', category: 'Edibles', subCategory: 'Beverages', thc: '10mg per bottle' },
  { name: 'CBD Tincture', category: 'CBD Products', subCategory: 'CBD Oil', cbd: '1000mg per bottle' },
  { name: 'Sleep Capsules', category: 'Edibles', subCategory: 'Capsules', thc: '5mg THC + 5mg CBN' },
];

/**
 * Concentrate products
 */
const concentrateProducts = [
  { name: 'Live Resin Cart', category: 'Vape Products', subCategory: 'Vape Cartridges', thc: '85-90%' },
  { name: 'Shatter', category: 'Cannabis Concentrates', subCategory: 'Shatter', thc: '80-90%' },
  { name: 'Rosin', category: 'Cannabis Concentrates', subCategory: 'Rosin', thc: '70-85%' },
  { name: 'Hash', category: 'Cannabis Concentrates', subCategory: 'Hash', thc: '40-60%' },
  { name: 'Distillate Syringe', category: 'Cannabis Concentrates', subCategory: 'Distillate', thc: '90-95%' },
];

/**
 * Generate products
 */
const generateProducts = async () => {
  try {
    const providers = await Provider.find();
    const categories = await Category.find();
    const subCategories = await SubCategory.find();
    
    if (providers.length === 0 || categories.length === 0 || subCategories.length === 0) {
      throw new Error('Missing required data. Please seed providers, categories, and subcategories first.');
    }
    
    const products = [];
    
    // Create flower products
    for (const strain of cannabisStrains) {
      const provider = faker.random.arrayElement(providers);
      
      let categoryName, subCategoryName;
      if (strain.type === 'Indica') {
        categoryName = 'Cannabis Flower';
        subCategoryName = 'Indica Strains';
      } else if (strain.type === 'Sativa') {
        categoryName = 'Cannabis Flower';
        subCategoryName = 'Sativa Strains';
      } else if (strain.type === 'Hybrid') {
        categoryName = 'Cannabis Flower';
        subCategoryName = 'Hybrid Strains';
      } else if (strain.type === 'CBD') {
        categoryName = 'Cannabis Flower';
        subCategoryName = 'High CBD Strains';
      }
      
      const category = categories.find(c => c.name === categoryName);
      const subCategory = subCategories.find(sc => sc.name === subCategoryName && sc.category.toString() === category._id.toString());
      
      if (category && subCategory) {
        const product = {
          provider: provider._id,
          name: strain.name,
          description: `Premium ${strain.type.toLowerCase()} strain with ${strain.thc} THC and ${strain.cbd} CBD. Known for ${strain.effects.join(', ').toLowerCase()} effects. Grown with care using organic methods.`,
          category: category._id,
          subCategory: subCategory._id,
          delivery: true,
          minDeliveryAmount: faker.datatype.number({ min: 25, max: 75 }),
          shippingCharges: faker.datatype.number({ min: 5, max: 15 }),
          photo: [
            `https://example.com/images/strains/${strain.name.toLowerCase().replace(/\s+/g, '-')}-1.jpg`,
            `https://example.com/images/strains/${strain.name.toLowerCase().replace(/\s+/g, '-')}-2.jpg`,
            `https://example.com/images/strains/${strain.name.toLowerCase().replace(/\s+/g, '-')}-3.jpg`,
          ],
        };
        
        products.push(product);
      }
    }
    
    // Create edible products
    for (const edible of edibleProducts) {
      const provider = faker.random.arrayElement(providers);
      const category = categories.find(c => c.name === edible.category);
      const subCategory = subCategories.find(sc => sc.name === edible.subCategory && sc.category.toString() === category._id.toString());
      
      if (category && subCategory) {
        const product = {
          provider: provider._id,
          name: edible.name,
          description: `Delicious ${edible.name.toLowerCase()} made with premium cannabis extract. ${edible.thc || edible.cbd}. Perfect for controlled dosing and long-lasting effects.`,
          category: category._id,
          subCategory: subCategory._id,
          delivery: true,
          minDeliveryAmount: faker.datatype.number({ min: 25, max: 50 }),
          shippingCharges: faker.datatype.number({ min: 5, max: 10 }),
          photo: [
            `https://example.com/images/edibles/${edible.name.toLowerCase().replace(/\s+/g, '-')}-1.jpg`,
            `https://example.com/images/edibles/${edible.name.toLowerCase().replace(/\s+/g, '-')}-2.jpg`,
          ],
        };
        
        products.push(product);
      }
    }
    
    // Create concentrate products
    for (const concentrate of concentrateProducts) {
      const provider = faker.random.arrayElement(providers);
      const category = categories.find(c => c.name === concentrate.category);
      const subCategory = subCategories.find(sc => sc.name === concentrate.subCategory && sc.category.toString() === category._id.toString());
      
      if (category && subCategory) {
        const product = {
          provider: provider._id,
          name: `${faker.random.arrayElement(cannabisStrains).name} ${concentrate.name}`,
          description: `High-quality ${concentrate.name.toLowerCase()} with ${concentrate.thc} THC. Extracted using premium methods for maximum potency and flavor.`,
          category: category._id,
          subCategory: subCategory._id,
          delivery: true,
          minDeliveryAmount: faker.datatype.number({ min: 50, max: 100 }),
          shippingCharges: faker.datatype.number({ min: 10, max: 20 }),
          photo: [
            `https://example.com/images/concentrates/${concentrate.name.toLowerCase().replace(/\s+/g, '-')}-1.jpg`,
            `https://example.com/images/concentrates/${concentrate.name.toLowerCase().replace(/\s+/g, '-')}-2.jpg`,
          ],
        };
        
        products.push(product);
      }
    }
    
    return products;
  } catch (error) {
    console.error('Error generating products:', error);
    throw error;
  }
};

/**
 * Seed products
 */
const seed = async () => {
  try {
    const products = await generateProducts();
    await Product.insertMany(products);
    console.log(`✅ Seeded ${products.length} products`);
  } catch (error) {
    console.error('❌ Error seeding products:', error);
    throw error;
  }
};

module.exports = {
  seed,
  generateProducts,
  cannabisStrains,
  edibleProducts,
  concentrateProducts,
};
