const Joi = require('joi');
const { Category, SubCategory } = require('../models');
const { exists, unique, objectId } = require('./custom.validation');

const category = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: Category, field: '_id', value });
    return value;
  });

const subCategory = {
  body: Joi.object({
    subCategory: Joi.string()
      .custom(objectId)
      .required()
      .external(async (value) => {
        await exists({ model: SubCategory, field: '_id', value });
        return value;
      }),
  }),
};

const params = {
  params: Joi.object({
    category,
  }),
};

const body = {
  body: Joi.object({
    category,
  }),
};

const create = {
  body: Joi.object({
    image: Joi.string().optional().allow(null),
    description:Joi.string().optional().allow(''),
    name: Joi.string()
      .required()
      .trim()
      .max(50)
      .external(async (value) => {
        await unique({ model: Category, field: 'name', value });
        return value;
      })
      .messages({
        'string.empty': '"name" cannot be empty',
        'string.max': '"name" must be less than or equal to 50 characters',
        'any.required': '"name" is required',
      }),
    status: Joi.boolean().required().messages({
      'any.required': '"status" is required',
    }),
    subCategories: Joi.array()
      .items(
        Joi.object({
          name: Joi.string()
            .required()
            .trim()
            .max(50)
            .external(async (value, helpers) => {
              const { category } = helpers.state.ancestors[0];
              await unique({ model: SubCategory, field: ['name', 'category'], value: [value, category] });
              return value;
            })
            .messages({
              'string.empty': '"subCategory" name cannot be empty',
              'string.max': '"subCategory" name must be less than or equal to 50 characters',
              'any.required': '"subCategory" name is required',
            }),
          status: Joi.boolean().required().messages({
            'any.required': '"status" is required',
          }),
        })
      )
      .min(1)
      .required()
      .messages({
        'array.base': '"subcategory" should be an array',
        'array.min': '"subcategory" must contain at least one item',
        'any.required': '"subcategory" is a required field',
      }),
  }),
};

const update = {
  body: Joi.object({
    category,
    image: Joi.string().uri().optional().allow(null),
    description:Joi.string().optional().allow(''),
    name: Joi.string()
      .trim()
      .required()
      .max(50)
      .external(async (value, helpers) => {
        const { category } = helpers.state.ancestors[0];
        await unique({ model: Category, field: 'name', value, excludeId: category });
        return value;
      })
      .messages({
        'string.empty': '"name" cannot be empty',
        'string.max': '"name" must be less than or equal to 50 characters',
      }),
    status: Joi.boolean().required().messages({
      'any.required': '"status" is required',
    }),
    subCategories: Joi.array()
      .items(
        Joi.object({
          _id: Joi.string()
            .optional()
            .custom(objectId)
            .external(async (value) => {
              if (value) {
                await exists({ model: SubCategory, field: '_id', value });
              }
              return value;
            })
            .messages({
              'string.base': '"id" must be a valid string',
              'any.required': '"id" is required if provided',
            }),
          status: Joi.boolean().optional().messages({
            'boolean.base': '"status" must be a boolean',
          }),
          name: Joi.string()
            .required()
            .trim()
            .max(50)
            .external(async (value, helpers) => {
              const { category } = helpers.state.ancestors[0];
              const excludeId = helpers.state.ancestors[0]._id; // Use if updating an existing subcategory
              await unique({
                model: SubCategory,
                field: ['name', 'category'],
                value: [value, category],
                excludeId,
              });
              return value;
            })
            .messages({
              'string.empty': '"subCategory" name cannot be empty',
              'string.max': '"subCategory" name must be less than or equal to 50 characters',
              'any.required': '"subCategory" name is required',
            }),
        })
      )
      .min(1)
      .required()
      .messages({
        'array.base': '"subcategory" should be an array',
        'array.min': '"subcategory" must contain at least one item',
        'any.required': '"subcategory" is a required field',
      }),
  }),
};

module.exports = {
  params,
  body,
  create,
  update,
  subCategory,
};
