const mongoose = require('mongoose');
const config = require('../src/config/config');
const logger = require('../src/config/logger');

// Import all seeders
const userSeeder = require('./userSeeder');
const categorySeeder = require('./categorySeeder');
const questionSeeder = require('./questionSeeder');
const providerSeeder = require('./providerSeeder');
const customerSeeder = require('./customerSeeder');
const productSeeder = require('./productSeeder');
const variantSeeder = require('./variantSeeder');
const productUsageSeeder = require('./productUsageSeeder');
const orderSeeder = require('./orderSeeder');
const reviewSeeder = require('./reviewSeeder');
const favouriteSeeder = require('./favouriteSeeder');
const notificationSeeder = require('./notificationSeeder');

// Import models for clearing
const {
  User,
  Category,
  SubCategory,
  Question,
  Provider,
  Customer,
  Product,
  Variant,
  ProductUsages,
  Review,
  Favourite,
  Notification,
} = require('../src/models');

// Import models with different names
const Option = require('../src/models/option.model');
const Order = require('../src/models/order.model');

/**
 * Clear all collections
 */
const clearDatabase = async () => {
  logger.info('Clearing database...');
  
  // Clear in reverse dependency order
  await Notification.deleteMany({});
  await Favourite.deleteMany({});
  await Review.deleteMany({});
  await Order.deleteMany({});
  await ProductUsages.deleteMany({});
  await Variant.deleteMany({});
  await Product.deleteMany({});
  await Customer.deleteMany({});
  await Provider.deleteMany({});
  await Option.deleteMany({});
  await Question.deleteMany({});
  await SubCategory.deleteMany({});
  await Category.deleteMany({});
  await User.deleteMany({});
  
  logger.info('Database cleared successfully');
};

/**
 * Run all seeders in correct order
 */
const runSeeders = async () => {
  logger.info('Starting database seeding...');
  
  try {
    // Seed in dependency order
    logger.info('Seeding users...');
    await userSeeder.seed();
    
    logger.info('Seeding categories and subcategories...');
    await categorySeeder.seed();
    
    logger.info('Seeding questions and options...');
    await questionSeeder.seed();
    
    logger.info('Seeding providers...');
    await providerSeeder.seed();
    
    logger.info('Seeding customers...');
    await customerSeeder.seed();
    
    logger.info('Seeding products...');
    await productSeeder.seed();
    
    logger.info('Seeding variants...');
    await variantSeeder.seed();
    
    logger.info('Seeding product usages...');
    await productUsageSeeder.seed();
    
    logger.info('Seeding orders...');
    await orderSeeder.seed();
    
    logger.info('Seeding reviews...');
    await reviewSeeder.seed();
    
    logger.info('Seeding favourites...');
    await favouriteSeeder.seed();
    
    logger.info('Seeding notifications...');
    await notificationSeeder.seed();
    
    logger.info('Database seeding completed successfully!');
  } catch (error) {
    logger.error('Error during seeding:', error);
    throw error;
  }
};

/**
 * Main seeder function
 */
const main = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(config.mongoose.url, config.mongoose.options);
    logger.info('Connected to MongoDB');
    
    // Check command line arguments
    const args = process.argv.slice(2);
    const shouldClear = args.includes('--clear');
    
    if (shouldClear) {
      await clearDatabase();
      logger.info('Database cleared. Exiting...');
    } else {
      // Clear database first, then seed
      await clearDatabase();
      await runSeeders();
    }
    
    // Close connection
    await mongoose.connection.close();
    logger.info('MongoDB connection closed');
    process.exit(0);
  } catch (error) {
    logger.error('Seeding failed:', error);
    await mongoose.connection.close();
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  main,
  runSeeders,
  clearDatabase,
};
