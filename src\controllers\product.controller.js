const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const GlobalService = require('../services/GlobalService');
const pick = require('../utils/pick');
const { sendSuccess, sendError } = require('../utils/ApiResponse');
const { UserPreference, Product, Provider } = require('../models');
const mongoose = require('mongoose');

const modelName = 'Product';
const resourceName = 'Product';
const resourcesName = 'Products';
const uniqueAttribs = [];
let aggregation = [
  {
    $lookup: {
      from: 'variants',
      localField: '_id',
      foreignField: 'product',
      as: 'variants',
      pipeline: [{ $match: { deleted: { $ne: true } } }, {
        $project: { __v: 0, deleted: 0, createdAt: 0, updatedAt: 0, product: 0 },
      }],
    },
  },
  {
    $lookup: {
      from: 'productusages',
      localField: '_id',
      foreignField: 'product',
      as: 'productUsages',
      pipeline: [{ $match: { deleted: { $ne: true } } }, {
        $project: { userDetails: 0, deleted: 0, __v: 0, deleted: 0, createdAt: 0, updatedAt: 0, product: 0 },
      },],
    },
  },
  {
    $lookup: {
      from: 'providers',
      localField: 'provider',
      foreignField: '_id',
      as: 'providerDetails',
      pipeline: [
        { $match: { deleted: { $ne: true } } }, // Filter providers where deleted is not true
        {
          $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'userDetails',
            pipeline: [
              {
                $project: {
                  _id: 0, // Exclude `_id` if not needed
                  email: 1, // Include only the `email` field
                },
              },
            ],
          },
        },
        {
          $addFields: {
            email: { $arrayElemAt: ['$userDetails.email', 0] }, // Extract the first email
          },
        },
        {
          $project: {
            userDetails: 0, deleted: 0 // Remove the `userDetails` array
          },
        },
      ],
    },
  },
  {
    $lookup: {
      from: 'categories',
      localField: 'category',
      foreignField: '_id',
      as: 'categoryDetails',
    },
  },
  {
    $lookup: {
      from: 'subcategories',
      localField: 'subCategory',
      foreignField: '_id',
      as: 'subCategoryDetails',
    },
  },
  {
    $addFields: {
      categoryName: { $arrayElemAt: ['$categoryDetails.name', 0] },
      subCategoryName: { $arrayElemAt: ['$subCategoryDetails.name', 0] },
    },
  },
  {
    $project: {
      categoryDetails: 0,
      subCategoryDetails: 0,
    },
  },
  {
    $sort: { _id: -1 }, // Use _id for reliable sorting (newer documents have higher _id values)
  }
];

const create = catchAsync(async (req, res) => {
  let payload;
  if (req.provider && req.provider._id) {
    payload = pick(req.body, ['name', 'description', 'category', 'subCategory', 'delivery', 'minDeliveryAmount', 'shippingCharges', 'photo']);
    payload.provider = req.provider._id;
  } else
    payload = pick(req.body, [
      'provider',
      'name',
      'description',
      'category',
      'subCategory',
      'delivery',
      'minDeliveryAmount',
      'shippingCharges',
      'photo',
    ]);
  const product = await GlobalService.create(modelName, payload);

  // Handle variants
  const variants = req.body.variants || [];
  for (const variant of variants) {
    variant.product = product._id;
    const newVariant = await GlobalService.create('Variant', variant);
  }

  // Handle product usages
  const productUsages = req.body.productUsages || [];
  for (const usage of productUsages) {
    usage.product = product._id;
    await GlobalService.create('ProductUsages', usage);
  }
  const result = { ...product._doc, variants, productUsages };
  sendSuccess(res, `${resourceName} created successfully!`, httpStatus.CREATED, result);
});

const index = catchAsync(async (req, res) => {
  let aggregation1 = [
    ...aggregation
  ];

  const options = req.queryOptions || pick(req.query, ['sortBy', 'limit', 'page']);
  const { search } = req.query;

  let pipeline = req.provider && req.provider._id ? [...aggregation1, { $match: { provider: req.provider._id } }] : aggregation1;

  // Add search filter after aggregation if search term is provided
  if (search && search.trim()) {
    const searchRegex = { $regex: search.trim(), $options: 'i' };
    pipeline.push({
      $match: {
        $or: [
          { name: searchRegex },
          { description: searchRegex },
          { categoryName: searchRegex },
          { subCategoryName: searchRegex }
        ]
      }
    });
  }

  const result = await GlobalService.getAll(modelName, options, pipeline);
  sendSuccess(res, resourcesName, httpStatus.OK, result);
});

const search = catchAsync(async (req, res) => {
  const { search = '', limit = 10, page = 1 } = req.query;
  const filter = {
    name: { $regex: search, $options: 'i' }, // case-insensitive match
  };
  const skip = (page - 1) * limit;
  const products = await Product.find(filter, { name: 1, photo: 1 })
    .skip(Number(skip))
    .limit(Number(limit));

  sendSuccess(res, 'Products fetched successfully!', httpStatus.OK, products);
});
const getProductByProviderId = catchAsync(async (req, res) => {
  const { providerId } = req.params;
  const { categoryId } = req.query; // get categoryId from query parameters

  if (!providerId) {
    return sendError(res, 'Provider ID is required', httpStatus.BAD_REQUEST);
  }

  const providerExists = await Provider.findById(providerId);
  if (!providerExists) {
    return sendError(res, `_id "${providerId}" not found`, httpStatus.BAD_REQUEST);
  }

  const matchStage = {
    provider: mongoose.Types.ObjectId(providerId),
  };

  if (categoryId) {
    matchStage.category = mongoose.Types.ObjectId(categoryId);
  }

  const customAggregation = [
    ...aggregation,
    { $match: matchStage },
  ];

  const products = await GlobalService.getAllWithoutPagination(modelName, customAggregation);

  sendSuccess(res, `Products for provider ${providerId} fetched successfully!`, httpStatus.OK, products);
});



const view = catchAsync(async (req, res) => {
  const productId = req.params.product;
  const product = await GlobalService.getById(
    modelName,
    productId,
    req.provider && req.provider._id ? [...aggregation, { $match: { provider: req.provider._id } }] : aggregation
  );
  if (!product) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  sendSuccess(res, resourceName, httpStatus.OK, product);
});


const update = catchAsync(async (req, res) => {
  const productId = req.body.product;
  let product = await GlobalService.getById(
    modelName,
    productId,
    req.provider && req.provider._id ? [...aggregation, { $match: { provider: req.provider._id } }] : aggregation
  );
  if (!product) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  const fieldsToPick = ['name', 'description', 'category', 'subCategory', 'delivery', 'minDeliveryAmount', 'shippingCharges'];
  if (req.body.photo) fieldsToPick.push('photo'); // Include 'photo' only if it's in req.body

  // Update product itself
  product = await GlobalService.updateById(
    modelName,
    productId,
    pick(req.body, ['name', 'description', 'category', 'subCategory', 'delivery', 'minDeliveryAmount', 'shippingCharges', 'photo']),
    req.provider && req.provider._id ? [...aggregation, { $match: { provider: req.provider._id } }] : aggregation
  );

  // Update variants
  const updatedVariants = req.body.variants || [];
  const existingVariantIds = product.variants.map((variant) => variant._id.toString());
  const variantIdsToUpdate = updatedVariants.map((variant) => variant._id).filter(Boolean);

  // Remove variants not present in update request
  const variantIdsToRemove = existingVariantIds.filter((id) => !variantIdsToUpdate.includes(id));
  for (const variantId of variantIdsToRemove) {
    await GlobalService.softDeleteById('Variant', uniqueAttribs, variantId);
  }

  // Add or update variants
  const variants = [];
  for (const variant of updatedVariants) {
    if (variant._id) {
      const updatedVariant = await GlobalService.updateById('Variant', variant._id, variant);
      variants.push(updatedVariant);
    } else {
      variant.product = productId;
      const newVariant = await GlobalService.create('Variant', variant);
      variants.push(newVariant);
    }
  }

  // Update product usages
  const updatedUsages = req.body.productUsages || [];
  const existingUsageIds = product.productUsages.map((usage) => usage._id.toString());
  const usageIdsToUpdate = updatedUsages.map((usage) => usage._id).filter(Boolean);

  // Remove usages not present in update request
  const usageIdsToRemove = existingUsageIds.filter((id) => !usageIdsToUpdate.includes(id));
  for (const usageId of usageIdsToRemove) {
    await GlobalService.softDeleteById('ProductUsages', uniqueAttribs, usageId);
  }

  // Add or update usages
  const productUsages = [];
  for (const usage of updatedUsages) {
    if (usage._id) {
      const updatedUsage = await GlobalService.updateById('ProductUsages', usage._id, usage);
      productUsages.push(updatedUsage);
    } else {
      usage.product = productId;
      const newUsage = await GlobalService.create('ProductUsages', usage);
      productUsages.push(newUsage);
    }
  }
  product.variants = variants;
  product.productUsages = productUsages;
  sendSuccess(res, `${resourceName} updated successfully!`, httpStatus.OK, product);
});

const softDelete = catchAsync(async (req, res) => {
  const product = await GlobalService.getById(
    modelName,
    req.body.product,
    req.provider && req.provider._id ? [...aggregation, { $match: { provider: req.provider._id } }] : aggregation
  );
  if (!product) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);

  if (product && product.variants) {
    for (const variantItem of product.variants) {
      await GlobalService.softDeleteById('Variant', uniqueAttribs, variantItem._id);
    }
  }
  if (product && product.productUsages) {
    for (const usagesItem of product.productUsages) {
      await GlobalService.softDeleteById('ProductUsages', uniqueAttribs, usagesItem._id);
    }
  }

  await GlobalService.softDeleteById(modelName, uniqueAttribs, req.body.product);
  sendSuccess(res, `${resourceName} deleted successfully!`, httpStatus.NO_CONTENT);
});

const getRecommendedProducts = catchAsync(async (req, res) => {
  const customerId = req.user._id;

  try {
    // Get user preferences
    const userPreferences = await UserPreference.find({ user: customerId });
    if (!userPreferences || userPreferences.length === 0) {
      return sendSuccess(res, 'No preferences found. Please set your preferences.', httpStatus.OK, []);
    }

    const userOptionIds = userPreferences.map(pref => pref.answer);

    // Match products with those options via productusages
    const matchingProducts = await GlobalService.getAllWithoutPagination('Product', [
      {
        $lookup: {
          from: 'productusages',
          localField: '_id',
          foreignField: 'product',
          as: 'matchingUsages',
          pipeline: [
            { $match: { deleted: { $ne: true }, option: { $in: userOptionIds } } }
          ]
        }
      },
      {
        $match: {
          'matchingUsages.0': { $exists: true } // Only include products with at least one match
        }
      },
      {
        $project: {
          name: 1,
          description: 1,
          price: 1,
          image: 1,
          createdAt: 1,
          matchScore: { $size: '$matchingUsages' }
        }
      },
      {
        $sort: { matchScore: -1, createdAt: -1 }
      }
    ]);

    return sendSuccess(
      res,
      'Recommended products fetched successfully!',
      httpStatus.OK,
      matchingProducts
    );

  } catch (error) {
    console.error('Error getting recommended products:', error);
    return sendError(res, 'Failed to get product recommendations.', httpStatus.INTERNAL_SERVER_ERROR);
  }
});


// const getRecommendedProducts = catchAsync(async (req, res) => {
//   const customerId = req.user._id;
//   const options = pick(req.query, ['limit', 'page']);
//   const limit = parseInt(options.limit) || 10;
//   const page = parseInt(options.page) || 1;

//   try {
//     const userPreferences = await UserPreference.find({ user: customerId });

//     if (!userPreferences || userPreferences.length === 0) {
//       return sendSuccess(res, 'No preferences set. Please set your preferences first.', httpStatus.OK, {
//         results: [],
//         page,
//         limit,
//         totalPages: 0,
//         totalResults: 0,
//         userPreferences: 0,
//         message: 'No preferences found. Please set your preferences to get personalized recommendations.'
//       });
//     }

//     // Step 2: Extract user's selected option IDs
//     const userOptionIds = userPreferences.map(pref => pref.answer);

//     // Step 3: Find products that match user preferences by checking productusage table
//     const matchingProducts = await GlobalService.getAllWithoutPagination('Product', [
//       {
//         $lookup: {
//           from: 'productusages',
//           localField: '_id',
//           foreignField: 'product',
//           as: 'matchingUsages',
//           pipeline: [
//             { $match: { deleted: { $ne: true } } },
//             {
//               $match: {
//                 option: { $in: userOptionIds }
//               }
//             }
//           ]
//         }
//       },
//       {
//         $match: {
//           'matchingUsages.0': { $exists: true } // Only products with matching preferences
//         }
//       },
//       // Add product details using the existing aggregation pipeline
//       {
//         $lookup: {
//           from: 'variants',
//           localField: '_id',
//           foreignField: 'product',
//           as: 'variants',
//           pipeline: [
//             { $match: { deleted: { $ne: true } } },
//             { $project: { __v: 0, deleted: 0, createdAt: 0, updatedAt: 0, product: 0 } }
//           ]
//         }
//       },
//       {
//         $lookup: {
//           from: 'productusages',
//           localField: '_id',
//           foreignField: 'product',
//           as: 'productUsages',
//           pipeline: [
//             { $match: { deleted: { $ne: true } } },
//             { $project: { userDetails: 0, deleted: 0, __v: 0, createdAt: 0, updatedAt: 0, product: 0 } }
//           ]
//         }
//       },
//       {
//         $lookup: {
//           from: 'providers',
//           localField: 'provider',
//           foreignField: '_id',
//           as: 'providerDetails',
//           pipeline: [
//             { $match: { deleted: { $ne: true } } },
//             {
//               $lookup: {
//                 from: 'users',
//                 localField: 'user',
//                 foreignField: '_id',
//                 as: 'userDetails',
//                 pipeline: [
//                   {
//                     $project: {
//                       _id: 0,
//                       email: 1,
//                     },
//                   },
//                 ],
//               },
//             },
//             {
//               $addFields: {
//                 email: { $arrayElemAt: ['$userDetails.email', 0] },
//               },
//             },
//             {
//               $project: {
//                 userDetails: 0, deleted: 0
//               },
//             },
//           ],
//         },
//       },
//       {
//         $lookup: {
//           from: 'categories',
//           localField: 'category',
//           foreignField: '_id',
//           as: 'categoryDetails',
//         },
//       },
//       {
//         $lookup: {
//           from: 'subcategories',
//           localField: 'subCategory',
//           foreignField: '_id',
//           as: 'subCategoryDetails',
//         },
//       },
//       {
//         $addFields: {
//           categoryName: { $arrayElemAt: ['$categoryDetails.name', 0] },
//           subCategoryName: { $arrayElemAt: ['$subCategoryDetails.name', 0] },
//           matchScore: { $size: '$matchingUsages' },
//           totalUserPreferences: userPreferences.length
//         },
//       },
//       {
//         $project: {
//           categoryDetails: 0,
//           subCategoryDetails: 0,
//         },
//       },
//       // Sort by match score (highest first), then by creation date
//       {
//         $sort: { matchScore: -1, createdAt: -1 }
//       }
//     ]);

//     // Step 4: Apply pagination
//     const skip = (page - 1) * limit;
//     const paginatedProducts = matchingProducts.slice(skip, skip + limit);
//     const totalResults = matchingProducts.length;
//     const totalPages = Math.ceil(totalResults / limit);

//     const result = {
//       results: paginatedProducts,
//       page,
//       limit,
//       totalPages,
//       totalResults,
//       userPreferences: userPreferences.length,
//       message: `Found ${totalResults} products matching your preferences!`
//     };

//     sendSuccess(res, 'Recommended products fetched successfully!', httpStatus.OK, result);

//   } catch (error) {
//     console.error('Error getting recommended products:', error);
//     return sendError(res, 'Failed to get personalized product recommendations', httpStatus.INTERNAL_SERVER_ERROR);
//   }
// });

module.exports = { create, index, view, update, softDelete, getRecommendedProducts, search, getProductByProviderId };
