const faker = require('faker');
const { User, Customer, Variant, Product, Provider } = require('../src/models');
const Order = require('../src/models/order.model');

/**
 * Generate orders
 */
const generateOrders = async () => {
  try {
    const customers = await Customer.find().populate('user');
    const variants = await Variant.find();
    const products = await Product.find();
    const providers = await Provider.find();

    if (customers.length === 0 || variants.length === 0) {
      throw new Error('Missing required data. Please seed customers and variants first.');
    }
    
    const orders = [];
    
    // Generate orders for each customer
    for (const customer of customers) {
      const numOrders = faker.datatype.number({ min: 0, max: 8 }); // Some customers have no orders
      
      for (let i = 0; i < numOrders; i++) {
        const variant = faker.random.arrayElement(variants);
        const product = products.find(p => p._id.toString() === variant.product.toString());

        if (!product) {
          console.log(`Skipping variant ${variant._id} - product not found`);
          continue;
        }

        // Get provider from product
        const provider = providers.find(p => p._id.toString() === product.provider.toString());
        
        const quantity = faker.datatype.number({ min: 1, max: 5 });
        const price = variant.price;
        const totalPrice = price * quantity;
        
        // Generate order date (within last 6 months)
        const orderDate = faker.date.recent(180);
        
        // Determine order status based on date
        let status;
        const daysSinceOrder = Math.floor((new Date() - orderDate) / (1000 * 60 * 60 * 24));

        if (daysSinceOrder < 1) {
          status = 'pending';
        } else if (daysSinceOrder < 3) {
          status = faker.random.arrayElement(['pending', 'completed']);
        } else if (daysSinceOrder < 7) {
          status = 'completed';
        } else {
          status = faker.random.arrayElement(['completed', 'cancelled_by_user', 'cancelled_by_provider']);
        }
        
        // Delivery preference
        const delivery = faker.random.arrayElement(['yes', 'no']);
        
        const order = {
          user_id: customer.user._id,
          variant_id: variant._id,
          provider_id: provider._id,
          product_id: product._id,
          quantity: quantity,
          price: price,
          total_price: totalPrice,
          delivery: delivery,
          status: status,
          shipping_address: {
            full_name: customer.username || faker.name.findName(),
            phone_number: customer.address?.phone || faker.phone.phoneNumber(),
            street: customer.address?.address_line || faker.address.streetAddress(),
            landmark: customer.address?.landmark || faker.address.secondaryAddress(),
            city: customer.address?.city || faker.address.city(),
            state: customer.address?.state || faker.address.state(),
            country: 'India',
            pincode: customer.address?.pincode || faker.address.zipCode(),
            latitude: faker.address.latitude(),
            longitude: faker.address.longitude(),
          },
        };
        
        // Set timestamps
        order.createdAt = orderDate;
        order.updatedAt = status === 'completed' ?
                         faker.date.between(orderDate, new Date()) :
                         faker.date.between(orderDate, new Date());
        
        orders.push(order);
      }
    }
    
    return orders;
  } catch (error) {
    console.error('Error generating orders:', error);
    throw error;
  }
};

/**
 * Seed orders
 */
const seed = async () => {
  try {
    const orders = await generateOrders();
    await Order.insertMany(orders);
    console.log(`✅ Seeded ${orders.length} orders`);
  } catch (error) {
    console.error('❌ Error seeding orders:', error);
    throw error;
  }
};

module.exports = {
  seed,
  generateOrders,
};
