const faker = require('faker');
const { User, Provider } = require('../src/models');

/**
 * Cannabis dispensary/provider names and descriptions
 */
const providerData = [
  {
    name: 'Green Leaf Dispensary',
    description: 'Premium cannabis products with a focus on organic, locally-grown strains. We offer a wide selection of flower, concentrates, and edibles.',
  },
  {
    name: 'Bud Master Cannabis Co.',
    description: 'Your trusted source for high-quality cannabis products. Specializing in craft cannabis and artisanal concentrates.',
  },
  {
    name: 'Herb Heaven',
    description: 'A boutique dispensary offering curated cannabis products for both medical and recreational users. Expert budtenders on staff.',
  },
  {
    name: 'Cannabis Corner',
    description: 'Medical cannabis dispensary focused on patient care and education. Wide variety of CBD and THC products for various conditions.',
  },
  {
    name: 'THC Therapy Center',
    description: 'Medical marijuana dispensary specializing in therapeutic cannabis products for chronic pain, anxiety, and other conditions.',
  },
  {
    name: 'Marijuana Mart',
    description: 'One-stop shop for all your cannabis needs. From flower to edibles to accessories, we have it all at competitive prices.',
  },
  {
    name: 'Weed <PERSON>',
    description: 'Premium cannabis retailer with a focus on exotic strains and innovative products. Knowledgeable staff and great customer service.',
  },
  {
    name: 'Cannabis Care Clinic',
    description: 'Medical cannabis dispensary with on-site consultations. Helping patients find the right products for their specific needs.',
  },
  {
    name: 'Green Garden Grow',
    description: 'Craft cannabis cultivator and dispensary. All products are grown in-house using sustainable, organic practices.',
  },
  {
    name: 'Bud Buddy Supply',
    description: 'Wholesale and retail cannabis supplier. Offering bulk pricing and a wide selection of products for all experience levels.',
  },
];

/**
 * Generate provider data
 */
const generateProviders = async () => {
  try {
    // Get provider users
    const providerUsers = await User.find({ role: 'provider' }).limit(10);
    
    if (providerUsers.length === 0) {
      throw new Error('No provider users found. Please seed users first.');
    }
    
    const providers = [];
    
    for (let i = 0; i < providerUsers.length && i < providerData.length; i++) {
      const user = providerUsers[i];
      const data = providerData[i];
      
      // Generate random coordinates for major cannabis-legal cities
      const locations = [
        { city: 'Los Angeles', state: 'CA', coords: [-118.2437, 34.0522] },
        { city: 'San Francisco', state: 'CA', coords: [-122.4194, 37.7749] },
        { city: 'Denver', state: 'CO', coords: [-104.9903, 39.7392] },
        { city: 'Seattle', state: 'WA', coords: [-122.3321, 47.6062] },
        { city: 'Portland', state: 'OR', coords: [-122.6784, 45.5152] },
        { city: 'Las Vegas', state: 'NV', coords: [-115.1398, 36.1699] },
        { city: 'Phoenix', state: 'AZ', coords: [-112.0740, 33.4484] },
        { city: 'Chicago', state: 'IL', coords: [-87.6298, 41.8781] },
        { city: 'Boston', state: 'MA', coords: [-71.0589, 42.3601] },
        { city: 'New York', state: 'NY', coords: [-74.0060, 40.7128] },
      ];

      const randomLocation = faker.random.arrayElement(locations);

      // Add some randomness to coordinates (within ~2 mile radius)
      const lat = randomLocation.coords[1] + faker.datatype.float({ min: -0.02, max: 0.02, precision: 0.0001 });
      const lng = randomLocation.coords[0] + faker.datatype.float({ min: -0.02, max: 0.02, precision: 0.0001 });

      const provider = {
        name: data.name,
        description: data.description,
        user: user._id,
        image: `https://example.com/images/providers/provider-${i + 1}.jpg`,
        photoId: faker.datatype.uuid(),
        cannabisLicense: `CL-${faker.random.alphaNumeric(8).toUpperCase()}`,
        resellersPermit: `RP-${faker.random.alphaNumeric(8).toUpperCase()}`,
        street: faker.address.streetAddress(),
        city: randomLocation.city,
        state: randomLocation.state,
        country: 'United States',
        zipCode: parseInt(faker.address.zipCode()),
        location: {
          type: 'Point',
          coordinates: [lng, lat], // [longitude, latitude]
        },
        radius: faker.datatype.number({ min: 5, max: 25 }),
        paymentOption: faker.random.arrayElements(['Credit Card', 'Debit Card', 'UPI', 'Net Banking'], faker.datatype.number({ min: 2, max: 4 })),
        startTime: '09:00',
        endTime: faker.random.arrayElement(['20:00', '21:00', '22:00']),
        availableDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
        isApproved: faker.random.arrayElement(['pending', 'approved']),
        rating: faker.datatype.float({ min: 3.5, max: 5.0, precision: 0.1 }),
        phone: faker.phone.phoneNumber(),
      };
      
      providers.push(provider);
    }
    
    return providers;
  } catch (error) {
    console.error('Error generating providers:', error);
    throw error;
  }
};

/**
 * Seed providers
 */
const seed = async () => {
  try {
    const providers = await generateProviders();
    await Provider.insertMany(providers);
    console.log(`✅ Seeded ${providers.length} providers`);
  } catch (error) {
    console.error('❌ Error seeding providers:', error);
    throw error;
  }
};

module.exports = {
  seed,
  generateProviders,
  providerData,
};
