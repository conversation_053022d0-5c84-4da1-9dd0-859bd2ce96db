const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const MongooseDelete = require('mongoose-delete');

const categorySchema = mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    status: {
      type: Boolean,
      default: true,
    },
    image: {
      type: String,
      required: false,
    },
    description:{
      type:String,
      required: false
    }
  },
  {
    timestamps: true,
  }
);

categorySchema.virtual('subCategory', {
  ref: 'SubCategory',
  localField: '_id',
  foreignField: 'category',
});
categorySchema.plugin(toJSON);
categorySchema.plugin(paginate);
categorySchema.plugin(MongooseDelete, { overrideMethods: 'all' });

/**
 * @typedef Category
 */
const Category = mongoose.model('category', categorySchema);

module.exports = Category;
