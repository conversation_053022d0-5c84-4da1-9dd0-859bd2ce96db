// Import all models
const Token = require('./token.model');
const User = require('./user.model');
const Category = require('./category.model');
const SubCategory = require('./subCategory.model');
const Customer = require('./customer.model');
const Question = require('./question.model');
const Option = require('./option.model');
const UserPreference = require('./userPreference.model');
// const PaymentOption = require('./paymentOption.model');
// const Unit = require('./unit.model');
const Provider = require('./provider.model');
const Product = require('./product.model');
const Variant = require('./variant.model');
const ProductUsages = require('./productUsages.model');


// ###################################################
// make order model
const Review = require('./reviews.model');
const Order = require('./order.model')
const Favourite = require('./favourite.model');
const Notification = require('./notification.model')
// ###################################################

// Export all models
const toExport = {
  Token,
  User,
  Category,
  SubCategory,
  Customer,
  Question,
  Option,
  UserPreference,
  Notification,
  // PaymentOption,
  // Unit,
  Provider,
  Product,
  Variant,
  ProductUsages,
  Favourite,
  Order,
  Review,
};

// Define the getModel function outside of module.exports
const getModel = (modelName) => {
  return model = toExport[modelName];
};

module.exports = {getModel, ...toExport}
