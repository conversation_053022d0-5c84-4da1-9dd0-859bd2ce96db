const { UserPreference, Question, Option } = require('../models');

/**
 * Get user preferences with full question and option details
 * @param {ObjectId} userId
 * @returns {Promise<Array>}
 */
const getUserPreferencesWithDetails = async (userId) => {
  const preferences = await UserPreference.find({ user: userId })
    .populate({
      path: 'question',
      select: '_id value sequence',
      match: { deleted: { $ne: true } }
    })
    .populate({
      path: 'answer',
      select: '_id value',
      match: { deleted: { $ne: true } }
    })
    .sort({ 'question.sequence': 1 });

  // Filter out preferences where question or answer was deleted
  return preferences.filter(pref => pref.question && pref.answer);
};

/**
 * Get user preference for a specific question with full details
 * @param {ObjectId} userId
 * @param {ObjectId} questionId
 * @returns {Promise<Object|null>}
 */
const getUserPreferenceByQuestion = async (userId, questionId) => {
  const preference = await UserPreference.findOne({
    user: userId,
    question: questionId
  })
    .populate({
      path: 'question',
      select: '_id value sequence',
      match: { deleted: { $ne: true } }
    })
    .populate({
      path: 'answer',
      select: '_id value',
      match: { deleted: { $ne: true } }
    });

  // Return null if preference doesn't exist or question/answer was deleted
  if (!preference || !preference.question || !preference.answer) {
    return null;
  }

  return preference;
};

/**
 * Check if user has completed all preference questions
 * @param {ObjectId} userId
 * @returns {Promise<Object>}
 */
const checkPreferenceCompletion = async (userId) => {
  // Get all active questions
  const allQuestions = await Question.find({ deleted: { $ne: true } })
    .select('_id value sequence')
    .sort({ sequence: 1 });

  // Get user's answered questions
  const userPreferences = await UserPreference.find({ user: userId })
    .populate({
      path: 'question',
      match: { deleted: { $ne: true } }
    });

  // Filter out preferences where question was deleted
  const validPreferences = userPreferences.filter(pref => pref.question);
  const answeredQuestionIds = validPreferences.map(pref => pref.question._id.toString());

  // Find missing questions
  const missingQuestions = allQuestions.filter(
    question => !answeredQuestionIds.includes(question._id.toString())
  );

  const totalQuestions = allQuestions.length;
  const answeredQuestions = validPreferences.length;
  const isComplete = missingQuestions.length === 0 && totalQuestions > 0;

  return {
    isComplete,
    totalQuestions,
    answeredQuestions,
    missingQuestions: missingQuestions.map(q => ({
      _id: q._id,
      value: q.value,
      sequence: q.sequence
    }))
  };
};

/**
 * Get user's preference option IDs for product matching
 * @param {ObjectId} userId
 * @returns {Promise<Array>}
 */
const getUserPreferenceOptionIds = async (userId) => {
  const preferences = await UserPreference.find({ user: userId })
    .populate({
      path: 'question',
      match: { deleted: { $ne: true } }
    })
    .populate({
      path: 'answer',
      match: { deleted: { $ne: true } }
    });

  // Filter out preferences where question or answer was deleted
  const validPreferences = preferences.filter(pref => pref.question && pref.answer);
  
  return validPreferences.map(pref => pref.answer._id);
};

/**
 * Update or create user preference
 * @param {ObjectId} userId
 * @param {ObjectId} questionId
 * @param {ObjectId} optionId
 * @returns {Promise<Object>}
 */
const setUserPreference = async (userId, questionId, optionId) => {
  // Verify question and option exist and are active
  const question = await Question.findOne({ _id: questionId, deleted: { $ne: true } });
  if (!question) {
    throw new Error('Question not found or inactive');
  }

  const option = await Option.findOne({ 
    _id: optionId, 
    question: questionId, 
    deleted: { $ne: true } 
  });
  if (!option) {
    throw new Error('Option not found or does not belong to this question');
  }

  // Update or create preference
  const preference = await UserPreference.findOneAndUpdate(
    { user: userId, question: questionId },
    { user: userId, question: questionId, answer: optionId },
    { upsert: true, new: true }
  );

  return preference;
};

/**
 * Delete user preference for a specific question
 * @param {ObjectId} userId
 * @param {ObjectId} questionId
 * @returns {Promise<Object>}
 */
const deleteUserPreference = async (userId, questionId) => {
  const result = await UserPreference.deleteOne({ 
    user: userId, 
    question: questionId 
  });
  
  return result;
};

/**
 * Get preference statistics for a user
 * @param {ObjectId} userId
 * @returns {Promise<Object>}
 */
const getUserPreferenceStats = async (userId) => {
  const completionStatus = await checkPreferenceCompletion(userId);
  const preferences = await getUserPreferencesWithDetails(userId);

  return {
    totalPreferences: preferences.length,
    completionPercentage: completionStatus.totalQuestions > 0 
      ? Math.round((completionStatus.answeredQuestions / completionStatus.totalQuestions) * 100)
      : 0,
    isComplete: completionStatus.isComplete,
    lastUpdated: preferences.length > 0 
      ? Math.max(...preferences.map(p => new Date(p.updatedAt).getTime()))
      : null
  };
};

module.exports = {
  getUserPreferencesWithDetails,
  getUserPreferenceByQuestion,
  checkPreferenceCompletion,
  getUserPreferenceOptionIds,
  setUserPreference,
  deleteUserPreference,
  getUserPreferenceStats
};
