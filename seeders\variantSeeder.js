const faker = require('faker');
const { Product, Variant, Category, SubCategory } = require('../src/models');

/**
 * Generate variants for products
 */
const generateVariants = async () => {
  try {
    const products = await Product.find();
    const categories = await Category.find();
    const subCategories = await SubCategory.find();

    if (products.length === 0) {
      throw new Error('No products found. Please seed products first.');
    }

    const variants = [];

    for (const product of products) {
      // Find category and subcategory by ID
      const category = categories.find(c => c._id.toString() === product.category.toString());
      const subCategory = subCategories.find(sc => sc._id.toString() === product.subCategory.toString());

      if (!category || !subCategory) {
        console.log(`Skipping product ${product.name} - missing category or subcategory`);
        continue;
      }

      const categoryName = category.name;
      const subCategoryName = subCategory.name;
      
      // Generate variants based on product category
      if (categoryName === 'Cannabis Flower') {
        // Flower variants - different weights
        const flowerVariants = [
          { amount: 1, unit: 'Gram', basePrice: 15, stock: faker.datatype.number({ min: 10, max: 50 }) },
          { amount: 3.5, unit: 'Gram', basePrice: 45, stock: faker.datatype.number({ min: 5, max: 30 }) },
          { amount: 7, unit: 'Gram', basePrice: 85, stock: faker.datatype.number({ min: 3, max: 20 }) },
          { amount: 14, unit: 'Gram', basePrice: 160, stock: faker.datatype.number({ min: 1, max: 15 }) },
          { amount: 28, unit: 'Gram', basePrice: 300, stock: faker.datatype.number({ min: 1, max: 10 }) },
        ];
        
        flowerVariants.forEach((variant, index) => {
          const priceVariation = faker.datatype.float({ min: 0.8, max: 1.3, precision: 0.01 });
          const price = Math.round(variant.basePrice * priceVariation);
          const fakePrice = Math.round(price * faker.datatype.float({ min: 1.1, max: 1.4, precision: 0.01 }));
          
          variants.push({
            product: product._id,
            amount: variant.amount,
            unit: variant.unit,
            default: index === 1, // 3.5g as default
            fake_price: fakePrice,
            price: price,
            stock: variant.stock,
          });
        });
        
      } else if (categoryName === 'Edibles') {
        // Edible variants - different quantities/strengths
        if (subCategoryName === 'Gummies') {
          const gummyVariants = [
            { amount: 10, unit: 'Piece', basePrice: 25, stock: faker.datatype.number({ min: 20, max: 100 }) },
            { amount: 20, unit: 'Piece', basePrice: 45, stock: faker.datatype.number({ min: 10, max: 50 }) },
            { amount: 50, unit: 'Piece', basePrice: 100, stock: faker.datatype.number({ min: 5, max: 30 }) },
          ];
          
          gummyVariants.forEach((variant, index) => {
            const priceVariation = faker.datatype.float({ min: 0.9, max: 1.2, precision: 0.01 });
            const price = Math.round(variant.basePrice * priceVariation);
            const fakePrice = Math.round(price * faker.datatype.float({ min: 1.1, max: 1.3, precision: 0.01 }));
            
            variants.push({
              product: product._id,
              amount: variant.amount,
              unit: variant.unit,
              default: index === 0, // 10 pieces as default
              fake_price: fakePrice,
              price: price,
              stock: variant.stock,
            });
          });
          
        } else if (subCategoryName === 'Chocolates') {
          const chocolateVariants = [
            { amount: 1, unit: 'Piece', basePrice: 20, stock: faker.datatype.number({ min: 15, max: 75 }) },
            { amount: 3, unit: 'Piece', basePrice: 55, stock: faker.datatype.number({ min: 8, max: 40 }) },
          ];
          
          chocolateVariants.forEach((variant, index) => {
            const priceVariation = faker.datatype.float({ min: 0.9, max: 1.2, precision: 0.01 });
            const price = Math.round(variant.basePrice * priceVariation);
            const fakePrice = Math.round(price * faker.datatype.float({ min: 1.1, max: 1.3, precision: 0.01 }));
            
            variants.push({
              product: product._id,
              amount: variant.amount,
              unit: variant.unit,
              default: index === 0,
              fake_price: fakePrice,
              price: price,
              stock: variant.stock,
            });
          });
          
        } else if (subCategoryName === 'Beverages') {
          const beverageVariants = [
            { amount: 1, unit: 'Piece', basePrice: 15, stock: faker.datatype.number({ min: 20, max: 100 }) },
            { amount: 6, unit: 'Piece', basePrice: 80, stock: faker.datatype.number({ min: 5, max: 30 }) },
          ];
          
          beverageVariants.forEach((variant, index) => {
            const priceVariation = faker.datatype.float({ min: 0.9, max: 1.2, precision: 0.01 });
            const price = Math.round(variant.basePrice * priceVariation);
            const fakePrice = Math.round(price * faker.datatype.float({ min: 1.1, max: 1.3, precision: 0.01 }));
            
            variants.push({
              product: product._id,
              amount: variant.amount,
              unit: variant.unit,
              default: index === 0,
              fake_price: fakePrice,
              price: price,
              stock: variant.stock,
            });
          });
        }
        
      } else if (categoryName === 'Cannabis Concentrates') {
        // Concentrate variants - different weights
        const concentrateVariants = [
          { amount: 0.5, unit: 'Gram', basePrice: 35, stock: faker.datatype.number({ min: 10, max: 40 }) },
          { amount: 1, unit: 'Gram', basePrice: 65, stock: faker.datatype.number({ min: 5, max: 25 }) },
          { amount: 2, unit: 'Gram', basePrice: 120, stock: faker.datatype.number({ min: 2, max: 15 }) },
        ];
        
        concentrateVariants.forEach((variant, index) => {
          const priceVariation = faker.datatype.float({ min: 0.8, max: 1.4, precision: 0.01 });
          const price = Math.round(variant.basePrice * priceVariation);
          const fakePrice = Math.round(price * faker.datatype.float({ min: 1.1, max: 1.5, precision: 0.01 }));
          
          variants.push({
            product: product._id,
            amount: variant.amount,
            unit: variant.unit,
            default: index === 0, // 0.5g as default
            fake_price: fakePrice,
            price: price,
            stock: variant.stock,
          });
        });
        
      } else if (categoryName === 'Vape Products') {
        // Vape variants - different sizes
        const vapeVariants = [
          { amount: 0.5, unit: 'Gram', basePrice: 45, stock: faker.datatype.number({ min: 15, max: 60 }) },
          { amount: 1, unit: 'Gram', basePrice: 80, stock: faker.datatype.number({ min: 8, max: 35 }) },
        ];
        
        vapeVariants.forEach((variant, index) => {
          const priceVariation = faker.datatype.float({ min: 0.9, max: 1.3, precision: 0.01 });
          const price = Math.round(variant.basePrice * priceVariation);
          const fakePrice = Math.round(price * faker.datatype.float({ min: 1.1, max: 1.4, precision: 0.01 }));
          
          variants.push({
            product: product._id,
            amount: variant.amount,
            unit: variant.unit,
            default: index === 0,
            fake_price: fakePrice,
            price: price,
            stock: variant.stock,
          });
        });
        
      } else if (categoryName === 'CBD Products') {
        // CBD variants - different strengths
        const cbdVariants = [
          { amount: 500, unit: 'Milliliter', basePrice: 40, stock: faker.datatype.number({ min: 20, max: 80 }) },
          { amount: 1000, unit: 'Milliliter', basePrice: 70, stock: faker.datatype.number({ min: 10, max: 50 }) },
          { amount: 2000, unit: 'Milliliter', basePrice: 130, stock: faker.datatype.number({ min: 5, max: 30 }) },
        ];
        
        cbdVariants.forEach((variant, index) => {
          const priceVariation = faker.datatype.float({ min: 0.9, max: 1.2, precision: 0.01 });
          const price = Math.round(variant.basePrice * priceVariation);
          const fakePrice = Math.round(price * faker.datatype.float({ min: 1.1, max: 1.3, precision: 0.01 }));
          
          variants.push({
            product: product._id,
            amount: variant.amount,
            unit: variant.unit,
            default: index === 1, // 1000ml as default
            fake_price: fakePrice,
            price: price,
            stock: variant.stock,
          });
        });
        
      } else {
        // Default variant for other categories
        const price = faker.datatype.number({ min: 20, max: 100 });
        const fakePrice = Math.round(price * faker.datatype.float({ min: 1.1, max: 1.4, precision: 0.01 }));
        
        variants.push({
          product: product._id,
          amount: 1,
          unit: 'Piece',
          default: true,
          fake_price: fakePrice,
          price: price,
          stock: faker.datatype.number({ min: 5, max: 50 }),
        });
      }
    }
    
    return variants;
  } catch (error) {
    console.error('Error generating variants:', error);
    throw error;
  }
};

/**
 * Seed variants
 */
const seed = async () => {
  try {
    const variants = await generateVariants();
    await Variant.insertMany(variants);
    console.log(`✅ Seeded ${variants.length} variants`);
  } catch (error) {
    console.error('❌ Error seeding variants:', error);
    throw error;
  }
};

module.exports = {
  seed,
  generateVariants,
};
