/* eslint-disable no-param-reassign */

const paginate = (schema) => {
  /**
   * @typedef {Object} QueryResult
   * @property {Document[]} results - Results found
   * @property {number} page - Current page
   * @property {number} limit - Maximum number of results per page
   * @property {number} totalPages - Total number of pages
   * @property {number} totalResults - Total number of documents
   */
  /**
   * Query for documents with pagination using aggregation
   * @param {Object} [options] - Query options
   * @param {string} [options.sortBy] - Sorting criteria using the format: sortField:(desc|asc). Multiple sorting criteria should be separated by commas (,)
   * @param {string} [options.populate] - Populate data fields. Hierarchy of fields should be separated by (.). Multiple populating criteria should be separated by commas (,)
   * @param {number} [options.limit] - Maximum number of results per page (default = 10)
   * @param {number} [options.page] - Current page (default = 1)
   * @param {string} [options.includeDeleted] - Flag to include deleted documents (optional, default = false)
   * @param {Array} [aggregation] - Custom aggregation pipeline stages
   * @returns {Promise<QueryResult>}
   */
  schema.statics.paginate = async function (options, aggregation = []) {
    let sort = {};
    if (options.sortBy) {
      options.sortBy.split(',').forEach((sortOption) => {
        const [key, order] = sortOption.split(':');
        sort[key] = order === 'desc' ? -1 : 1;
      });
    } else {
      // Use _id as default sort instead of createdAt for better reliability
      sort = { _id: -1 }; // Default sort using _id which is always available
    }

    const limit = options.limit && parseInt(options.limit, 10) > 0 ? parseInt(options.limit, 10) : 10;
    const page = options.page && parseInt(options.page, 10) > 0 ? parseInt(options.page, 10) : 1;
    const skip = (page - 1) * limit;

    // If 'includeDeleted' is not set, exclude deleted documents by default
    let filter = {};
    if (!options.includeDeleted) {
      filter.deleted = { $ne: true }; // Exclude soft-deleted documents by default
    }

    // Build pipeline - check if aggregation already has sorting
    const hasExistingSort = aggregation.some(stage => stage.$sort);

    // Add filter and sort to the pipeline
    const pipeline = [
      { $match: filter }, // Match documents based on filter
      ...aggregation, // Add custom aggregation stages if provided
      // Only add sort if aggregation doesn't already have one
      ...(hasExistingSort ? [] : [{ $sort: sort }]), // Sort documents conditionally
      {
        $project: {
          __v: 0,
          deleted: 0,
        }
      }
    ];

    // Count total documents for pagination
    const totalResultsPipeline = [...pipeline, { $count: 'total' }];
    const totalResultsPromise = this.aggregate(totalResultsPipeline).exec();

    // Add pagination stages
    pipeline.push(
      { $skip: skip },
      { $limit: limit }
    );

    // Execute the aggregation pipeline
    const resultsPromise = this.aggregate(pipeline).exec();

    // Resolve both promises (results and total count)
    return Promise.all([totalResultsPromise, resultsPromise]).then(([totalResults, results]) => {
      const totalCount = totalResults[0]?.total || 0;
      const totalPages = Math.ceil(totalCount / limit);
      const result = {
        results,
        page,
        limit,
        totalPages,
        totalResults: totalCount,
      };
      return Promise.resolve(result);
    });
  };
};

module.exports = paginate;
