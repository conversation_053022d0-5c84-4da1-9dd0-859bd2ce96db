/* This JavaScript code defines a module that handles operations related to managing user favorites.
Here's a breakdown of what each part of the code does: */
const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const GlobalService = require('../services/GlobalService');
const pick = require('../utils/pick');
const { sendSuccess, sendError } = require('../utils/ApiResponse');
const mongoose = require('mongoose');

const modelName = 'Favourite';
const resourceName = 'Favourite';
const resourcesName = 'Favourites';

// Create / Add Favourite
const create = catchAsync(async (req, res) => {
  const { userId, variantId } = req.body;

  if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(variantId)) {
    return sendError(res, 'Invalid user ID or variant ID', httpStatus.BAD_REQUEST);
  }
  const existingFavourite = await GlobalService.getOne(modelName, { userId, variantId });
  if (existingFavourite) {
    return sendError(res, `${resourceName} already exists!`, httpStatus.CONFLICT);
  }
  const favourite = await GlobalService.create(modelName, { userId, variantId });
  sendSuccess(res, `${resourceName} added successfully!`, httpStatus.CREATED, favourite);
});



//  Get Favourites by userId
// Get Favourites by userId
const getFavourites = catchAsync(async (req, res) => {
  const userId = req.user._id;
  const options = pick(req.query, ['sortBy', 'limit', 'page']);

  // Validate user ID
  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return sendError(res, 'Valid user ID is required', httpStatus.BAD_REQUEST);
  }

  const favourites = await GlobalService.getAll(modelName, {
    ...options,
    filter: {
      userId,
      isLiked: true,
    },
    populate: [
      {
        path: 'variant',
        select: 'price fakePrice product',
        populate: {
          path: 'product',
          select: 'name photo',
        },
      },
    ],
  });

  // Handle no results
  if (!favourites?.results || favourites.results.length === 0) {
    return sendError(
      res, 
      `No ${resourcesName} found for this user`, 
      httpStatus.NOT_FOUND
    );
  }

  // Format results with proper data access
  const formattedFavourites = favourites.results.map(fav => {
    // Extract populated data
    const variant = fav.variantId;
    const product = variant?.product; // Access populated product object
    
    return {
      _id: fav._id,
      isLiked: fav.isLiked,
      userId: fav.userId,
      variant: {
        _id: variant?._id,
        price: variant?.price,
        fakePrice: variant?.fakePrice,
        product: {
          _id: product?._id,
          name: product?.name || null,
          // Handle single string or array of photos
          image: product?.photo 
            ? (Array.isArray(product.photo) 
                ? product.photo[0] 
                : product.photo)
            : null
        }
      }
    };
  });

  sendSuccess(
    res,
    `${resourcesName} fetched successfully!`,
    httpStatus.OK,
    formattedFavourites
  );
});

  

//  Toggle Like / Unlike
const toggleLike = catchAsync(async (req, res) => {
  const { userId, variantId } = req.body;

  if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(variantId)) {
    return sendError(res, 'Invalid user ID or variant ID', httpStatus.BAD_REQUEST);
  }
  const existingFavourite = await GlobalService.getOne(modelName, { userId, variantId });
  if (!existingFavourite) {
    return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  } 

  const updatedFavourite = await GlobalService.updateById(
    modelName,
    existingFavourite._id,
    { isLiked: !existingFavourite.isLiked }
  );

  sendSuccess(
    res,
    `Favourite ${updatedFavourite.isLiked ? 'liked' : 'unliked'} successfully!`,
    httpStatus.OK,
    updatedFavourite
  );
});



module.exports = {
  create,
  getFavourites,
  toggleLike,
};
