const faker = require('faker');
const { Review } = require('../src/models');
const Order = require('../src/models/order.model');

/**
 * Cannabis product review templates
 */
const reviewTemplates = {
  positive: {
    titles: [
      'Amazing quality!',
      'Exceeded expectations',
      'Perfect for my needs',
      'Great product',
      'Highly recommend',
      'Excellent strain',
      'Top quality',
      'Love this product',
      'Perfect effects',
      'Outstanding quality',
    ],
    experiences: [
      'This product delivered exactly what I was looking for. Great quality and effects.',
      'Smooth experience with perfect potency. Will definitely order again.',
      'Excellent for pain relief and relaxation. Highly recommend to others.',
      'Perfect for evening use. Helped me sleep better than anything else.',
      'Great taste and aroma. The effects were exactly as described.',
      'Fast-acting and long-lasting. Perfect for my medical needs.',
      'Clean, pure product with consistent quality. Very satisfied.',
      'Helped tremendously with my anxiety. Great for stress relief.',
      'Perfect balance of effects. Not too strong, not too weak.',
      'Excellent customer service and product quality. Will be back.',
    ],
  },
  neutral: {
    titles: [
      'Good product',
      'Decent quality',
      'As expected',
      'Fair value',
      'Okay experience',
      'Average quality',
      'Not bad',
      'Reasonable price',
      'Standard product',
      'Acceptable quality',
    ],
    experiences: [
      'Product was as described. Nothing exceptional but did the job.',
      'Decent quality for the price. Would consider ordering again.',
      'Average experience. Product worked but nothing special.',
      'Fair quality and pricing. Met my basic expectations.',
      'Okay product. Effects were mild but present.',
      'Standard quality. Nothing to complain about but not amazing.',
      'Product was fine. Delivery was quick and packaging was good.',
      'Reasonable value for money. Effects were as expected.',
      'Decent strain. Would try other products from this provider.',
      'Average experience overall. Product was okay.',
    ],
  },
  negative: {
    titles: [
      'Not what I expected',
      'Disappointing quality',
      'Could be better',
      'Not impressed',
      'Below expectations',
      'Poor value',
      'Not satisfied',
      'Quality issues',
      'Overpriced',
      'Not effective',
    ],
    experiences: [
      'Product quality was below expectations. Effects were weaker than described.',
      'Overpriced for what you get. Have found better quality elsewhere.',
      'Not very effective for my needs. Would not recommend.',
      'Quality was inconsistent. Some parts were good, others not so much.',
      'Expected better based on the description. Somewhat disappointed.',
      'Product was okay but not worth the premium price.',
      'Effects were too mild for my tolerance. Need something stronger.',
      'Packaging was poor and product seemed old. Not fresh.',
      'Not impressed with the quality. Have had better experiences.',
      'Product did not meet my expectations. Looking for alternatives.',
    ],
  },
};

/**
 * Generate reviews
 */
const generateReviews = async () => {
  try {
    const completedOrders = await Order.find({ status: 'completed' });

    if (completedOrders.length === 0) {
      console.log('No completed orders found. Skipping review seeding.');
      return [];
    }
    
    const reviews = [];
    
    for (const order of completedOrders) {
      // Not all orders get reviews (about 60% review rate)
      if (!faker.datatype.boolean() || faker.datatype.float({ min: 0, max: 1, precision: 0.01 }) > 0.6) {
        continue;
      }
      
      // Determine review sentiment based on various factors
      let sentiment;
      const randomFactor = faker.datatype.float({ min: 0, max: 1, precision: 0.01 });
      
      if (randomFactor < 0.7) {
        sentiment = 'positive'; // 70% positive reviews
      } else if (randomFactor < 0.9) {
        sentiment = 'neutral'; // 20% neutral reviews
      } else {
        sentiment = 'negative'; // 10% negative reviews
      }
      
      // Generate rating based on sentiment
      let rating;
      if (sentiment === 'positive') {
        rating = faker.datatype.number({ min: 4, max: 5 });
      } else if (sentiment === 'neutral') {
        rating = faker.datatype.number({ min: 3, max: 4 });
      } else {
        rating = faker.datatype.number({ min: 1, max: 3 });
      }
      
      // Select title and experience based on sentiment
      const template = reviewTemplates[sentiment];
      const title = faker.random.arrayElement(template.titles);
      const experience = faker.random.arrayElement(template.experiences);
      
      // Generate review date (after order delivery)
      const reviewDate = faker.date.between(
        order.updatedAt || order.createdAt,
        new Date()
      );
      
      const review = {
        order_id: order._id,
        rating: rating,
        title: title,
        experience: experience,
        image: faker.datatype.boolean() ? 
               `https://example.com/images/reviews/review-${faker.datatype.uuid()}.jpg` : null,
        createdAt: reviewDate,
        updatedAt: reviewDate,
      };
      
      reviews.push(review);
    }
    
    return reviews;
  } catch (error) {
    console.error('Error generating reviews:', error);
    throw error;
  }
};

/**
 * Seed reviews
 */
const seed = async () => {
  try {
    const reviews = await generateReviews();
    if (reviews.length > 0) {
      await Review.insertMany(reviews);
      console.log(`✅ Seeded ${reviews.length} reviews`);
    } else {
      console.log('⚠️ No reviews to seed (no delivered orders found)');
    }
  } catch (error) {
    console.error('❌ Error seeding reviews:', error);
    throw error;
  }
};

module.exports = {
  seed,
  generateReviews,
  reviewTemplates,
};
