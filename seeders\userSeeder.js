const faker = require('faker');
const bcrypt = require('bcryptjs');
const { User } = require('../src/models');

/**
 * Generate user data
 */
const generateUsers = async () => {
  const users = [];
  
  // Create admin user
  users.push({
    email: '<EMAIL>',
    password: await bcrypt.hash('password123', 8),
    role: 'admin',
  });
  
  // Create provider users (cannabis dispensary owners)
  const providerEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ];
  
  for (const email of providerEmails) {
    users.push({
      email,
      password: await bcrypt.hash('provider123', 8),
      role: 'provider',
    });
  }
  
  // Create customer users
  for (let i = 0; i < 50; i++) {
    users.push({
      email: faker.internet.email().toLowerCase(),
      password: await bcrypt.hash('customer123', 8),
      role: 'customer',
      fcmToken: faker.datatype.boolean() ? faker.datatype.uuid() : null,
    });
  }
  
  return users;
};

/**
 * Seed users
 */
const seed = async () => {
  try {
    const users = await generateUsers();
    await User.insertMany(users);
    console.log(`✅ Seeded ${users.length} users`);
  } catch (error) {
    console.error('❌ Error seeding users:', error);
    throw error;
  }
};

module.exports = {
  seed,
  generateUsers,
};
