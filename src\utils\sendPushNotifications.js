// utils/sendPushNotification.js
// const fetch = require('node-fetch');
const { sendSuccess, sendError } = require('./ApiResponse');

const sendPushNotification = async (deviceToken, title, body) => {
  const fetch = (await import('node-fetch')).default; 
  const serverKey = process.env.FIREBASE_SERVER_KEY;

  if (!serverKey) {
    console.error('Missing Firebase Server Key');
    return;
  }

  const message = {
    to: deviceToken,
    notification: {
      title: title,
      body: body,
      sound: 'default',
    },
    priority: 'high',
  };

  try {
    const response = await fetch('https://fcm.googleapis.com/fcm/send', {
      method: 'POST',
      headers: {
        Authorization: `key=${serverKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });

    const data = await response.json();
    sendSuccess(response,'Push notification response:', data);

    return data;
  } catch (error) {
    sendError('Error sending push notification:', error);
  }
};

module.exports = { sendPushNotification };
