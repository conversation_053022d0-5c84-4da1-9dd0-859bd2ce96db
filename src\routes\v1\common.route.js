// ********************** Common routes for provider and customer **********************
const express = require('express');
const { categoryController, questionController } = require('../../controllers');
const auth = require('../../middlewares/auth');

const router = express.Router();

// ############## Category Routes ##############
router.get('/category', auth('commonAccess'), categoryController.fetch);
router.get('/question', auth('commonAccess'), questionController.fetch);

module.exports = router;
