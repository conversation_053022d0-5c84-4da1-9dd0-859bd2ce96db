const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const MongooseDelete = require('mongoose-delete');

const productSchema = mongoose.Schema(
  {
    provider: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Provider',
      required: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
      trim: true,
    },
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category',
      required: true,
    },
    subCategory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SubCategory',
      required: true,
    },
    delivery: {
      type: Boolean,
      default: true,
    },
    minDeliveryAmount: {
      type: Number,
      required: true,
      default: 0,
    },
    shippingCharges: {
      type: Number,
      default: 0,
    },
    photo: {
      type: [String],
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add plugin that converts mongoose to json
productSchema.plugin(toJSON);
productSchema.plugin(paginate);
productSchema.plugin(MongooseDelete, { overrideMethods: 'all' });

/**
 * @typedef product
 */
const product = mongoose.model('product', productSchema);
module.exports = product;
