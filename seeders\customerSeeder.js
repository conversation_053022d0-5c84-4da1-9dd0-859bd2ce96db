const faker = require('faker');
const { User, Customer } = require('../src/models');

/**
 * Generate customer data
 */
const generateCustomers = async () => {
  try {
    // Get customer users
    const customerUsers = await User.find({ role: 'customer' });
    
    if (customerUsers.length === 0) {
      throw new Error('No customer users found. Please seed users first.');
    }
    
    const customers = [];
    
    for (const user of customerUsers) {
      // Generate random coordinates for major cannabis-legal cities
      const locations = [
        { city: 'Los Angeles', state: 'CA', coords: [-118.2437, 34.0522] },
        { city: 'San Francisco', state: 'CA', coords: [-122.4194, 37.7749] },
        { city: 'Denver', state: 'CO', coords: [-104.9903, 39.7392] },
        { city: 'Seattle', state: 'WA', coords: [-122.3321, 47.6062] },
        { city: 'Portland', state: 'OR', coords: [-122.6784, 45.5152] },
        { city: 'Las Vegas', state: 'NV', coords: [-115.1398, 36.1699] },
        { city: 'Phoenix', state: 'AZ', coords: [-112.0740, 33.4484] },
        { city: 'Chicago', state: 'IL', coords: [-87.6298, 41.8781] },
        { city: 'Boston', state: 'MA', coords: [-71.0589, 42.3601] },
        { city: 'New York', state: 'NY', coords: [-74.0060, 40.7128] },
      ];
      
      const randomLocation = faker.random.arrayElement(locations);
      
      // Add some randomness to coordinates (within ~5 mile radius)
      const lat = randomLocation.coords[1] + faker.datatype.float({ min: -0.05, max: 0.05, precision: 0.0001 });
      const lng = randomLocation.coords[0] + faker.datatype.float({ min: -0.05, max: 0.05, precision: 0.0001 });
      
      const customer = {
        user: user._id,
        username: faker.internet.userName().toLowerCase(),
        emailPermission: faker.datatype.boolean(),
        photo: faker.random.boolean() ? faker.image.avatar() : null,
        bio: faker.random.boolean() ? faker.lorem.sentence() : null,
        notificationPermission: faker.datatype.boolean(),
        location: {
          type: 'Point',
          coordinates: [lng, lat], // [longitude, latitude]
        },
        address: {
          phone: faker.phone.phoneNumber(),
          pincode: faker.address.zipCode(),
          locality: faker.address.secondaryAddress(),
          address_line: faker.address.streetAddress(),
          city: randomLocation.city,
          state: randomLocation.state,
          landmark: faker.random.boolean() ? faker.address.secondaryAddress() : null,
        },
        preferences: {
          preferredCategories: faker.random.arrayElements([
            'Cannabis Flower',
            'Edibles',
            'Concentrates',
            'CBD Products',
            'Vape Products',
            'Topicals',
          ], faker.datatype.number({ min: 1, max: 3 })),
          budgetRange: faker.random.arrayElement(['Budget', 'Mid-range', 'Premium', 'Luxury']),
          experienceLevel: faker.random.arrayElement(['Beginner', 'Intermediate', 'Advanced', 'Expert']),
          medicalPatient: faker.datatype.boolean(),
          preferredEffects: faker.random.arrayElements([
            'Relaxation',
            'Energy',
            'Focus',
            'Pain Relief',
            'Sleep',
            'Creativity',
            'Mood Enhancement',
          ], faker.datatype.number({ min: 1, max: 4 })),
        },
        loyaltyPoints: faker.datatype.number({ min: 0, max: 1000 }),
        totalOrders: faker.datatype.number({ min: 0, max: 50 }),
        totalSpent: faker.datatype.float({ min: 0, max: 5000, precision: 0.01 }),
        lastOrderDate: faker.random.boolean() ? faker.date.recent(90) : null,
        isActive: faker.datatype.boolean(),
        verificationStatus: faker.random.arrayElement(['pending', 'verified', 'rejected']),
        medicalCard: {
          hasCard: faker.datatype.boolean(),
          cardNumber: faker.datatype.boolean() ? `MC-${faker.random.alphaNumeric(8).toUpperCase()}` : null,
          expiryDate: faker.datatype.boolean() ? faker.date.future(2) : null,
          issuingState: faker.datatype.boolean() ? randomLocation.state : null,
        },
      };
      
      customers.push(customer);
    }
    
    return customers;
  } catch (error) {
    console.error('Error generating customers:', error);
    throw error;
  }
};

/**
 * Seed customers
 */
const seed = async () => {
  try {
    const customers = await generateCustomers();
    await Customer.insertMany(customers);
    console.log(`✅ Seeded ${customers.length} customers`);
  } catch (error) {
    console.error('❌ Error seeding customers:', error);
    throw error;
  }
};

module.exports = {
  seed,
  generateCustomers,
};
