const { sendProviderStatusChangeEmail, sendUserRegistrationEmail } = require('./email.service');
const logger = require('../config/logger');

/**
 * Simple Background Email Service
 * Handles email sending in background with retry logic
 */
class BackgroundEmailService {
  constructor() {
    this.queue = [];
    this.processing = false;
    this.maxRetries = 3;
    this.retryDelay = 2000; // 2 seconds
  }

  /**
   * Add email job to background queue
   * @param {string} type - Email type
   * @param {Object} data - Email data
   * @param {Object} options - Job options
   */
  addJob(type, data, options = {}) {
    const job = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      options: {
        priority: options.priority || 5,
        maxRetries: options.maxRetries || this.maxRetries,
        ...options
      },
      attempts: 0,
      createdAt: new Date(),
      status: 'queued'
    };

    // Insert job based on priority (higher priority first)
    const insertIndex = this.queue.findIndex(queuedJob => queuedJob.options.priority < job.options.priority);
    if (insertIndex === -1) {
      this.queue.push(job);
    } else {
      this.queue.splice(insertIndex, 0, job);
    }

    logger.info(`Email job queued: ${job.id} - ${type}`);
    
    // Start processing if not already running
    this.processQueue();
    
    return job.id;
  }

  /**
   * Process jobs in background
   */
  async processQueue() {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;

    while (this.queue.length > 0) {
      const job = this.queue.shift();
      await this.processJob(job);
    }

    this.processing = false;
  }

  /**
   * Process individual job
   * @param {Object} job - Job to process
   */
  async processJob(job) {
    job.attempts++;
    job.status = 'processing';

    try {
      logger.info(`Processing email job: ${job.id} - ${job.type} (attempt ${job.attempts})`);

      switch (job.type) {
        case 'provider_status_change':
          await sendProviderStatusChangeEmail(
            job.data.email,
            job.data.providerName,
            job.data.status
          );
          break;
        case 'user_registration':
          await sendUserRegistrationEmail(
            job.data.email,
            job.data.userName,
            job.data.userRole
          );
          break;
        default:
          throw new Error(`Unknown email job type: ${job.type}`);
      }

      job.status = 'completed';
      job.completedAt = new Date();
      logger.info(`Email job completed: ${job.id} - sent to ${job.data.email}`);

    } catch (error) {
      job.status = 'failed';
      job.error = error.message;
      logger.error(`Email job failed: ${job.id} - ${error.message}`);

      // Retry logic
      if (job.attempts < job.options.maxRetries) {
        job.status = 'retrying';
        logger.info(`Retrying email job: ${job.id} in ${this.retryDelay}ms`);
        
        setTimeout(() => {
          this.queue.unshift(job); // Add back to front of queue
          this.processQueue();
        }, this.retryDelay * job.attempts); // Exponential backoff
      } else {
        logger.error(`Email job permanently failed: ${job.id} after ${job.attempts} attempts`);
      }
    }
  }

  /**
   * Queue user registration welcome email
   * @param {string} email - User email
   * @param {string} userName - User name
   * @param {string} userRole - User role (customer, provider)
   * @returns {string} Job ID
   */
  queueUserRegistrationEmail(email, userName, userRole) {
    const priority = 9; // High priority for welcome emails

    return this.addJob('user_registration', {
      email,
      userName,
      userRole
    }, { priority });
  }

  /**
   * Queue provider status change email
   * @param {string} email - Provider email
   * @param {string} providerName - Provider name
   * @param {string} status - New status
   * @returns {string} Job ID
   */
  queueProviderStatusChangeEmail(email, providerName, status) {
    const priority = this.getStatusPriority(status);

    return this.addJob('provider_status_change', {
      email,
      providerName,
      status
    }, { priority });
  }

  /**
   * Get priority based on status
   * @param {string} status - Status type
   * @returns {number} Priority (higher = more important)
   */
  getStatusPriority(status) {
    const priorities = {
      approved: 10,
      rejected: 8,
      pending: 5
    };
    return priorities[status] || 1;
  }

  /**
   * Get queue statistics
   * @returns {Object} Queue stats
   */
  getStats() {
    const stats = {
      total: this.queue.length,
      processing: this.processing,
      byStatus: {},
      byType: {}
    };

    this.queue.forEach(job => {
      stats.byStatus[job.status] = (stats.byStatus[job.status] || 0) + 1;
      stats.byType[job.type] = (stats.byType[job.type] || 0) + 1;
    });

    return stats;
  }

  /**
   * Clear completed and failed jobs older than specified time
   * @param {number} maxAge - Max age in milliseconds (default: 1 hour)
   */
  cleanup(maxAge = 60 * 60 * 1000) {
    const cutoff = new Date(Date.now() - maxAge);
    const initialLength = this.queue.length;
    
    this.queue = this.queue.filter(job => {
      if (['completed', 'failed'].includes(job.status)) {
        const jobTime = job.completedAt || job.createdAt;
        return jobTime > cutoff;
      }
      return true;
    });

    const cleaned = initialLength - this.queue.length;
    if (cleaned > 0) {
      logger.info(`Cleaned ${cleaned} old email jobs from queue`);
    }
  }
}

// Create singleton instance
const backgroundEmailService = new BackgroundEmailService();

// Auto-cleanup every 30 minutes
setInterval(() => {
  backgroundEmailService.cleanup();
}, 30 * 60 * 1000);

module.exports = backgroundEmailService;
