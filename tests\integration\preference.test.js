const request = require('supertest');
const httpStatus = require('http-status');
const app = require('../../src/app');
const setupTestDB = require('../utils/setupTestDB');
const { User, Question, Option, Product, ProductUsages, UserPreference } = require('../../src/models');
const { userOne, admin, insertUsers } = require('../fixtures/user.fixture');
const { userOneAccessToken, adminAccessToken } = require('../fixtures/token.fixture');

setupTestDB();

describe('Preference System', () => {
  let question1, question2, option1, option2, option3, option4, product1;

  beforeEach(async () => {
    await insertUsers([userOne, admin]);

    // Create test questions
    question1 = await Question.create({
      value: 'What type of cuisine do you prefer?',
      sequence: 1
    });

    question2 = await Question.create({
      value: 'What is your preferred meal time?',
      sequence: 2
    });

    // Create test options
    option1 = await Option.create({
      question: question1._id,
      value: 'Italian'
    });

    option2 = await Option.create({
      question: question1._id,
      value: 'Chinese'
    });

    option3 = await Option.create({
      question: question2._id,
      value: 'Lunch'
    });

    option4 = await Option.create({
      question: question2._id,
      value: 'Dinner'
    });

    // Create a test product
    product1 = await Product.create({
      provider: userOne._id,
      name: 'Margherita Pizza',
      description: 'Classic Italian pizza',
      category: userOne._id, // Using userOne._id as placeholder
      subCategory: userOne._id, // Using userOne._id as placeholder
      delivery: true,
      minDeliveryAmount: 100,
      shippingCharges: 50,
      photo: ['test-image.jpg']
    });

    // Create product usages (product matches Italian cuisine and Dinner)
    await ProductUsages.create({
      product: product1._id,
      question: question1._id,
      option: option1._id // Italian
    });

    await ProductUsages.create({
      product: product1._id,
      question: question2._id,
      option: option4._id // Dinner
    });
  });

  describe('GET /v1/common/question', () => {
    test('should return 200 and questions with options', async () => {
      const res = await request(app)
        .get('/v1/common/question')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.data).toHaveLength(2);
      expect(res.body.data[0]).toHaveProperty('options');
      expect(res.body.data[0].options).toHaveLength(2);
    });
  });

  describe('POST /v1/customer/preference', () => {
    test('should return 201 and save user preference', async () => {
      const preferenceData = {
        question: question1._id.toString(),
        answer: option1._id.toString()
      };

      const res = await request(app)
        .post('/v1/customer/preference')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .send(preferenceData)
        .expect(httpStatus.CREATED);

      expect(res.body.message).toBe('User preference upserted successfully!');

      // Verify preference was saved
      const savedPreference = await UserPreference.findOne({
        user: userOne._id,
        question: question1._id
      });
      expect(savedPreference).toBeTruthy();
      expect(savedPreference.answer.toString()).toBe(option1._id.toString());
    });

    test('should update existing preference', async () => {
      // First, create a preference
      await UserPreference.create({
        user: userOne._id,
        question: question1._id,
        answer: option1._id
      });

      // Then update it
      const updateData = {
        question: question1._id.toString(),
        answer: option2._id.toString()
      };

      await request(app)
        .post('/v1/customer/preference')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .send(updateData)
        .expect(httpStatus.CREATED);

      // Verify preference was updated
      const updatedPreference = await UserPreference.findOne({
        user: userOne._id,
        question: question1._id
      });
      expect(updatedPreference.answer.toString()).toBe(option2._id.toString());
    });
  });

  describe('GET /v1/customer/preferences', () => {
    test('should return 200 and user preferences with completion status', async () => {
      // Create some preferences
      await UserPreference.create({
        user: userOne._id,
        question: question1._id,
        answer: option1._id
      });

      const res = await request(app)
        .get('/v1/customer/preferences')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.data).toHaveProperty('preferences');
      expect(res.body.data).toHaveProperty('completionStatus');
      expect(res.body.data.preferences).toHaveLength(1);
      expect(res.body.data.completionStatus.isComplete).toBe(false);
      expect(res.body.data.completionStatus.totalQuestions).toBe(2);
      expect(res.body.data.completionStatus.answeredQuestions).toBe(1);
    });
  });

  describe('GET /v1/customer/products/recommended', () => {
    test('should return 404 when user has no preferences', async () => {
      const res = await request(app)
        .get('/v1/customer/products/recommended')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.NOT_FOUND);

      expect(res.body.message).toBe('No user preferences found. Please set your preferences first.');
    });

    test('should return 200 and recommended products based on preferences', async () => {
      // Create user preferences that match the product
      await UserPreference.create({
        user: userOne._id,
        question: question1._id,
        answer: option1._id // Italian
      });

      await UserPreference.create({
        user: userOne._id,
        question: question2._id,
        answer: option4._id // Dinner
      });

      const res = await request(app)
        .get('/v1/customer/products/recommended')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.data.results).toHaveLength(1);
      expect(res.body.data.results[0]._id.toString()).toBe(product1._id.toString());
      expect(res.body.data.results[0]).toHaveProperty('matchScore');
      expect(res.body.data.results[0].matchScore).toBe(2); // Matches 2 preferences
    });

    test('should return empty results when no products match preferences', async () => {
      // Create user preferences that don't match any product
      await UserPreference.create({
        user: userOne._id,
        question: question1._id,
        answer: option2._id // Chinese (product is Italian)
      });

      const res = await request(app)
        .get('/v1/customer/products/recommended')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.data.results).toHaveLength(0);
    });

    test('should support pagination', async () => {
      // Create user preferences
      await UserPreference.create({
        user: userOne._id,
        question: question1._id,
        answer: option1._id
      });

      const res = await request(app)
        .get('/v1/customer/products/recommended?page=1&limit=5')
        .set('Authorization', `Bearer ${userOneAccessToken}`)
        .expect(httpStatus.OK);

      expect(res.body.data).toHaveProperty('page');
      expect(res.body.data).toHaveProperty('limit');
      expect(res.body.data).toHaveProperty('totalPages');
      expect(res.body.data).toHaveProperty('totalResults');
      expect(res.body.data.page).toBe(1);
      expect(res.body.data.limit).toBe(5);
    });
  });
});
