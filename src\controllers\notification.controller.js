const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const Notification = require('../models/notification.model');
const UserPreference = require('../models/userPreference.model');
const ProductUsages = require('../models/productUsages.model');
// const Product = require('../models/product.model');
const Provider = require('../models/provider.model');
const GlobalService = require('../services/GlobalService'); // If you're using a global service pattern
const User = require('../models/user.model');
const ApiError = require('../utils/ApiError');
const { Product } = require('../models');
const Customer = require('../models/customer.model');
const mongoose = require('mongoose')
const { sendSuccess, sendError } = require('../utils/ApiResponse');
const pick = require('../utils/pick');
const { sendPushNotification } = require('../utils/sendPushNotifications');

const modelName = 'Notification';
const resourceName = 'Notification';
const resourcesName = 'Notifications';
const uniqueAttribs = []; 

const saveFcmToken = catchAsync(async (req, res) => {
  const { fcmToken } = req.body; // Assuming userId is not needed
  if (!fcmToken) {
    return sendError(res, 'FCM Token is required', httpStatus.BAD_REQUEST);
  }
  // Assuming you have a way to identify the user, e.g., from the token
  const userId = req.user._id; // Example of extracting userId from the request
  console.log('User ID:', userId);
  const updatedUser = await User.findByIdAndUpdate(userId, { fcmToken }, { new: true });
  if (!updatedUser) {
    return sendError(res, 'User not found', httpStatus.NOT_FOUND);
  }
  console.log('Saved FCM token for user:', userId);
  return sendSuccess(res, 'FCM token saved successfully!', httpStatus.OK, updatedUser);
});


const findNearbyMatchingProviders = async (customerId) => {
  console.log(`Starting matching process for customerId: ${customerId}`);

  const customer = await Customer.findById(customerId);
  if (!customer) {
    console.error(`Customer not found with ID: ${customerId}`);
    throw new ApiError(httpStatus.NOT_FOUND, 'Customer not found');
  }

  if (
    !customer.location ||
    !customer.location.coordinates ||
    customer.location.coordinates.length !== 2
  ) {
    console.error(`Incomplete customer location data for customerId: ${customerId}`);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Customer location is incomplete');
  }

  console.log('Customer location verified', customer.location.coordinates);

  let nearbyProviders = [];
  try {
    nearbyProviders = await Provider.find({
      location: {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: customer.location.coordinates,
          },
          $maxDistance: 100000, // 100km radius
        },
      },
      isApproved: 'approved',
    });
  } catch (err) {
    console.error('Error fetching nearby providers:', err);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to find nearby providers');
  }

  if (nearbyProviders.length === 0) {
    console.log('No providers found within 10km radius.');
    return {
      customer,
      matches: [],
    };
  }

  console.log(`Found ${nearbyProviders.length} nearby providers`);

  let preferences = [];
  try {
    preferences = await UserPreference.find({ user: customer.user });
  } catch (err) {
    console.error('Error fetching user preferences:', err);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to get customer preferences');
  }

  const customerPreferredOptionIds = preferences.map((pref) => pref.answer.toString());
  console.log(`Customer has ${customerPreferredOptionIds.length} preferences`);

  const matches = [];

  for (const provider of nearbyProviders) {
    try {
      const providerProductIds = await getProviderProducts(provider._id);

      const providerProductUsages = await ProductUsages.find({
        product: { $in: providerProductIds },
      });

      const providerOptionIds = providerProductUsages.map((usage) => usage.option.toString());

      const hasMatch = customerPreferredOptionIds.some((optionId) =>
        providerOptionIds.includes(optionId)
      );

      if (hasMatch) {
        matches.push({
          providerId: provider._id,
          providerName: provider.name,
          providerUserId: provider.user,
        });
      }
    } catch (err) {
      console.error(`Error processing provider ${provider._id}:`, err);
    }
  }

  return {
    customer,
    matches,
  };
};

// --------------------------------------------

const notifyCustomerAndProviders = async (customerId) => {
  try {
    const { customer, matches } = await findNearbyMatchingProviders(customerId);

    if (!matches || matches.length === 0) {
      console.log('No matching providers found to notify.');
      return {
        totalNotifications: 0,
        matches: [],
      };
    }

    let totalNotifications = 0;

    for (const match of matches) {
      try {
        // Notify Customer
        await Notification.create({
          receiver: customerId,
          message: `${match.providerName} near you has products matching your preferences!`,
          type: 'provider_to_customer',
          status: 'unread',
        });

        // Notify Provider
        await Notification.create({
          receiver: match.providerUserId,
          message: `A nearby customer (${customer.name}) has preferences matching your products!`,
          type: 'customer_to_provider',
          status: 'unread',
        });

        if (customer.fcmToken) {
          await sendPushNotification(
            customer.fcmToken,
            'Provider Match Nearby!',
            `${match.providerName} has products for you!`
          );
        }
        const provider = await Provider.findById(match.providerId);
        if (!provider) {
          console.error(`Provider not found with ID: ${match.providerId}`);
          continue;
        }

        const providerUser = await User.findById(provider.user);
if (!providerUser) {
  console.error(`User not found for provider: ${provider._id}`);
  continue;
}

        // Send Push to Provider
        if (providerUser.fcmToken) {
          await sendPushNotification(
            providerUser.fcmToken,
            'New Customer Nearby!',
            `${customer.username || 'A customer'} is interested in your products!`
          );
        }

        console.log(
          `Notifications sent between customer (${customerId}) and provider (${match.providerName})`
        );

        totalNotifications++;
      } catch (err) {
        console.error(`Error sending notification to provider ${match.providerId}:`, err);
      }
    }

    return {
      totalNotifications,
      matches,
    };
  } catch (error) {
    console.error('Unexpected error in notifyCustomerAndProviders:', error);
    throw error;
  }
};



/**
 * Helper: Get products of a provider
 */
const getProviderProducts = async (providerId) => {
  try {
    const products = await Product.find({ provider: providerId });
    console.log(products);
    return products.map((p) => p._id);
  
  } catch (err) {
    console.error(`Error fetching products for providerId: ${providerId}`, err);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to fetch provider products');
  }
};

const getNotifications = catchAsync(async (req, res) => {
  const options = req.queryOptions || pick(req.query, ['sortBy', 'limit', 'page']);
  const searchFilter = req.searchFilter;
  const notification = await GlobalService.getAll(modelName, options, [], searchFilter);

  // Return data even if empty - this is normal behavior for list endpoints
  sendSuccess(res, `${resourcesName} fetched successfully!`, httpStatus.OK, notification)
});


const updateNotificationStatus = catchAsync(async (req, res) => {
  const { notificationId } = req.params;
  const { status } = req.body;

  // Validate notificationId
  if (!mongoose.Types.ObjectId.isValid(notificationId)) {
    return sendError(res, 'Invalid notification ID', httpStatus.BAD_REQUEST);
  }

  // Get the existing notification
  const notification = await GlobalService.getById(modelName, notificationId);

  if (!notification) {
    return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  }

  // Prevent changing from 'read' back to 'unread'
  if (notification.status === 'read' && status === 'unread') {
    return sendError(
      res,
      'Cannot change status from "read" back to "unread"',
      httpStatus.BAD_REQUEST
    );
  }

  // Pick allowed fields to update (currently only status)
  const updatedData = {};
  
  if (status && notification.status !== status) {
    updatedData.status = status;
  }

  // If there's nothing to update, respond early
  if (Object.keys(updatedData).length === 0) {
    return sendSuccess(res, 'No changes detected.', httpStatus.OK, notification);
  }

  // Update notification using GlobalService
  const updatedNotification = await GlobalService.updateById(modelName, notificationId, updatedData);

  sendSuccess(res, `${resourceName} status updated successfully!`, httpStatus.OK, updatedNotification);
});



const softDelete = catchAsync(async (req, res) => {
  const notificationId = req.body.notificationId;
  const notification = await GlobalService.getById(modelName, notificationId);

  if (!notification) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);

  await GlobalService.softDeleteById(modelName, uniqueAttribs, notificationId);
  sendSuccess(res, `${resourceName} deleted successfully!`, httpStatus.NO_CONTENT);
});



const getNearbyProductsWithDelivery = async (customerId) => {
  try {
    // Get nearby matching providers based on customer preferences
    const { matches } = await findNearbyMatchingProviders(customerId);
    if (!matches || matches.length === 0) {
      console.log('No matching providers found for the customer.');
      return [];
    }
    const productsWithDelivery = [];
    // Loop through each matching provider to get their products
    for (const match of matches) {
      try {
        // Fetch products for the provider where delivery is true
        const products = await Product.find({ provider: match.providerId, delivery: true });
        
        // Log the products that match the criteria
        console.log(`Products for provider ${match.providerName} with delivery:`, products);
        // Add products to the result array
        productsWithDelivery.push(...products);
      } catch (err) {
        console.error(`Error fetching products for provider ${match.providerId}:`, err);
      }
    }
    return productsWithDelivery;
  } catch (error) {
    console.error('Unexpected error in getNearbyProductsWithDelivery:', error);
    throw error;
  }
};





module.exports = {
  softDelete,
  getNotifications,
  saveFcmToken,
  updateNotificationStatus,
  notifyCustomerAndProviders,
  getNearbyProductsWithDelivery
};
