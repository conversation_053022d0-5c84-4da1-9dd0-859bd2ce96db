const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const MongooseDelete = require('mongoose-delete');

const productUsagesSchema = mongoose.Schema(
  {
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true,
    },
    question: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Question',
      required: true,
    },
    option: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Option',
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add plugin that converts mongoose to json
productUsagesSchema.plugin(toJSON);
productUsagesSchema.plugin(paginate);
productUsagesSchema.plugin(MongooseDelete, { overrideMethods: 'all' });

/**
 * @typedef productUsages
 */
const productUsages = mongoose.model('productUsages', productUsagesSchema);
module.exports = productUsages;
