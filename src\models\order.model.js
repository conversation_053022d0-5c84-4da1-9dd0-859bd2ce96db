const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const orderSchema = mongoose.Schema(
  {
    user_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Users',
      required: true,
    },
    variant_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'variants',
      required: true,
    },
    provider_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Providers', 
    },
    
    product_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'product',
    },
    quantity: {
      type: Number,
      required: true,
      min: 1,
    },
    price: {
      type: Number,
      required: true,
    },
    total_price: {
      type: Number,
      required: true,
    },
  
    delivery: {
      type: String,
      enum: ['yes', 'no'],
      default: 'yes',
    },
    shipping_address: {
      full_name: { type: String, required: true }, 
      phone_number: { type: String }, 
      street: { type: String, required: true },
      landmark: { type: String },
      city: { type: String, required: true },
      state: { type: String, required: true },
      country: { type: String, required: true, default: 'India' },
      pincode: { type: String, required: true },
      latitude: { type: String },
      longitude: { type: String },
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'cancelled_by_user', 'cancelled_by_provider'],
      default: 'pending', 
    }

  },
  {
    timestamps: true,
  }
);


orderSchema.plugin(toJSON);
orderSchema.plugin(paginate);

const Orders = mongoose.model('Orders', orderSchema);
module.exports = Orders;
