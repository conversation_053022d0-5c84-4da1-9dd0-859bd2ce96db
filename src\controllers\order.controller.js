const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const GlobalService = require('../services/GlobalService');
const pick = require('../utils/pick');
const { sendSuccess, sendError } = require('../utils/ApiResponse');
const mongoose = require('mongoose');
const modelName = 'Order';
const resourceName = 'Order';
const resourcesName = 'Orders';
const uniqueAttribs = ['_id'];

// Full aggregation for detailed view (single order)
const fullAggregation = [
  {
    $lookup: {
      from: 'users',
      localField: 'user_id',
      foreignField: '_id',
      as: 'user',
      pipeline: [{ $project: { password: 0, __v: 0 } }],
    },
  },
  {
    $unwind: {
      path: '$user',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $lookup: {
      from: 'variants',
      localField: 'variant_id',
      foreignField: '_id',
      as: 'variant',
      pipeline: [{ $project: { __v: 0 } }],
    },
  },
  {
    $unwind: {
      path: '$variant',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $lookup: {
      from: 'products',
      localField: 'product_id',
      foreignField: '_id',
      as: 'product',
      pipeline: [{ $project: { __v: 0 } }],
    },
  },
  {
    $unwind: {
      path: '$product',
      preserveNullAndEmptyArrays: true,
    },
  },
];



// Create Order
const create = catchAsync(async (req, res) => {
  const orderData = pick(req.body, [
    'user_id',
    'variant_id',
    'provider_id',
    'product_id',
    'quantity',
    'price',
    'total_price',
    'delivery',
    'shipping_address',
    'status',
  ]);

  const order = await GlobalService.create(modelName, orderData);
  sendSuccess(res, `${resourceName} created successfully!`, httpStatus.CREATED, order);
});

//  Get Single Order By ID
const view = catchAsync(async (req, res) => {
  const order = await GlobalService.getById(modelName, req.params.order, fullAggregation);
  if (!order) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  sendSuccess(res, `${resourceName} fetched successfully!`, httpStatus.OK, order);
});


// Get Order List (optimized with only essential fields)
const index = catchAsync(async (req, res) => {
  const options = req.queryOptions || pick(req.query, ['sortBy', 'limit', 'page']);
  const { search } = req.query;

  // Handle additional filters from query parameters (all optional)
  const additionalFilters = {};

  // Only add filters if they are provided
  if (req.query.user_id) {
    additionalFilters.user_id = mongoose.Types.ObjectId(req.query.user_id);
  }
  if (req.query.provider_id) {
    additionalFilters.provider_id = mongoose.Types.ObjectId(req.query.provider_id);
  }
  if (req.provider && req.provider._id) {
    additionalFilters.provider_id = mongoose.Types.ObjectId(req.provider._id);
  }

  // For customer routes, filter by user_id from token (only if no explicit user_id provided)
  // Check if this is a customer request (not admin or provider)
  if (req.user && req.user._id && !req.query.user_id && !req.provider && req.user.role !== 'admin') {
    additionalFilters.user_id = mongoose.Types.ObjectId(req.user._id);
  }

  // Create optimized aggregation pipeline with only required fields
  const optimizedAggregation = [
    // Lookup user for email
    {
      $lookup: {
        from: 'users',
        localField: 'user_id',
        foreignField: '_id',
        as: 'user',
        pipeline: [{ $project: { email: 1 } }],
      },
    },
    { $unwind: { path: '$user', preserveNullAndEmptyArrays: true } },

    // Lookup variant for flavour
    {
      $lookup: {
        from: 'variants',
        localField: 'variant_id',
        foreignField: '_id',
        as: 'variant',
        pipeline: [{ $project: { flavour: 1 } }],
      },
    },
    { $unwind: { path: '$variant', preserveNullAndEmptyArrays: true } },

    // Lookup product for name and shipping charges
    {
      $lookup: {
        from: 'products',
        localField: 'product_id',
        foreignField: '_id',
        as: 'product',
        pipeline: [{ $project: { name: 1, shippingCharges: 1 } }],
      },
    },
    { $unwind: { path: '$product', preserveNullAndEmptyArrays: true } },

    // Lookup provider for provider name
    {
      $lookup: {
        from: 'providers',
        localField: 'provider_id',
        foreignField: '_id',
        as: 'provider',
        pipeline: [{ $project: { name: 1 } }],
      },
    },
    { $unwind: { path: '$provider', preserveNullAndEmptyArrays: true } },

    // Project only the required fields
    {
      $project: {
        _id: 0, // Remove the original _id
        order_id: '$_id',
        product_name: '$product.name',
        variant_flavour: '$variant.flavour',
        quantity: 1,
        price: 1,
        status: 1,
        order_date: '$createdAt',
        customer_email: '$user.email',
        shipping_charges: { $ifNull: ['$product.shippingCharges', 0] },
        provider_id: '$provider_id',
        provider_name: '$provider.name'
      }
    }
  ];

  // Create a pipeline with initial filters
  let pipeline = [...optimizedAggregation];

  // Apply initial filters if any
  if (Object.keys(additionalFilters).length > 0) {
    pipeline.unshift({ $match: additionalFilters });
  }

  // Add search filter after aggregation if search term is provided
  if (search && search.trim()) {
    const searchRegex = { $regex: search.trim(), $options: 'i' };
    pipeline.push({
      $match: {
        $or: [
          { product_name: searchRegex },
          { provider_name: searchRegex }
        ]
      }
    });
  }

  const result = await GlobalService.getAll(modelName, options, pipeline);

  sendSuccess(res, `${resourcesName} fetched successfully!`, httpStatus.OK, result);
});

//################# Update status ##################
const updateStatus = catchAsync(async (req, res) => {
  const { orderId, status } = req.body;

  let validStatuses = ['pending', 'cancelled_by_user'];

  if (req.provider && req.provider._id) {
    validStatuses = ['pending', 'completed', 'cancelled_by_user', 'cancelled_by_provider'];
  }

  if (!validStatuses.includes(status)) {
    return sendError(res, 'Invalid status value', httpStatus.BAD_REQUEST);
  }

  const existingOrder = await GlobalService.getById(modelName, orderId);

  if (!existingOrder) {
    return sendError(res, 'Order not found', httpStatus.NOT_FOUND);
  }

  const restrictedStatuses = ['completed', 'cancelled_by_user', 'cancelled_by_provider'];
  if (restrictedStatuses.includes(existingOrder.status)) {
    return sendError(res, `Order status cannot be changed from ${existingOrder.status}`, httpStatus.BAD_REQUEST);
  }

  const updatedOrder = await GlobalService.updateById(modelName, orderId, { status });

  sendSuccess(res, `Order status updated successfully!`, httpStatus.OK, updatedOrder);
});

module.exports = {
  create,
  index,
  view,
  updateStatus,
};
