const faker = require('faker');
const { User, Customer, Provider, Product, Notification } = require('../src/models');
const Option = require('../src/models/option.model');

/**
 * Notification message templates
 */
const notificationTemplates = {
  provider_to_customer: [
    'New products available! Check out our latest cannabis strains.',
    'Special discount on your favorite products - 20% off this week!',
    'Your order has been confirmed and is being prepared.',
    'Your order is ready for pickup at our dispensary.',
    'New CBD products now available - perfect for wellness.',
    'Flash sale: Buy 2 get 1 free on selected edibles!',
    'Your order has been shipped and is on the way.',
    'Thank you for your order! We appreciate your business.',
    'New high-quality concentrates just arrived in stock.',
    'Weekend special: Free delivery on orders over $75.',
  ],
  customer_to_provider: [
    'Question about product availability and pricing.',
    'Inquiry about delivery options to my area.',
    'Request for product recommendations based on my needs.',
    'Question about medical cannabis consultation.',
    'Feedback about recent purchase and experience.',
    'Request for bulk pricing information.',
    'Question about product testing and lab results.',
    'Inquiry about loyalty program and rewards.',
    'Request for information about new product arrivals.',
    'Question about return and exchange policy.',
  ],
};

/**
 * Generate notifications
 */
const generateNotifications = async () => {
  try {
    const customers = await Customer.find().populate('user');
    const providers = await Provider.find().populate('user');
    const products = await Product.find();
    const options = await Option.find();
    
    if (customers.length === 0 || providers.length === 0) {
      throw new Error('Missing required data. Please seed customers and providers first.');
    }
    
    const notifications = [];
    
    // Generate provider-to-customer notifications
    for (const customer of customers) {
      const numNotifications = faker.datatype.number({ min: 0, max: 8 });
      
      for (let i = 0; i < numNotifications; i++) {
        const provider = faker.random.arrayElement(providers);
        const message = faker.random.arrayElement(notificationTemplates.provider_to_customer);
        
        // Randomly select products for the notification
        const notificationProducts = [];
        if (products.length > 0 && faker.datatype.boolean()) {
          const selectedProducts = faker.random.arrayElements(
            products.filter(p => p.provider.toString() === provider._id.toString()),
            faker.datatype.number({ min: 1, max: 3 })
          );
          
          for (const product of selectedProducts) {
            notificationProducts.push({
              product: product._id,
              option: options.length > 0 ? faker.random.arrayElement(options)._id : null,
            });
          }
        }
        
        const notification = {
          receiver: customer.user._id,
          message: message,
          type: 'provider_to_customer',
          status: faker.random.arrayElement(['unread', 'read']),
          provider: provider._id,
          products: notificationProducts,
          createdAt: faker.date.recent(30), // Within last 30 days
          updatedAt: faker.date.recent(15), // Updated within last 15 days
        };
        
        notifications.push(notification);
      }
    }
    
    // Generate customer-to-provider notifications
    for (const provider of providers) {
      const numNotifications = faker.datatype.number({ min: 0, max: 5 });
      
      for (let i = 0; i < numNotifications; i++) {
        const customer = faker.random.arrayElement(customers);
        const message = faker.random.arrayElement(notificationTemplates.customer_to_provider);
        
        // Randomly select products for the notification
        const notificationProducts = [];
        if (products.length > 0 && faker.datatype.boolean()) {
          const selectedProducts = faker.random.arrayElements(
            products.filter(p => p.provider.toString() === provider._id.toString()),
            faker.datatype.number({ min: 1, max: 2 })
          );
          
          for (const product of selectedProducts) {
            notificationProducts.push({
              product: product._id,
              option: options.length > 0 ? faker.random.arrayElement(options)._id : null,
            });
          }
        }
        
        const notification = {
          receiver: provider.user._id,
          message: message,
          type: 'customer_to_provider',
          status: faker.random.arrayElement(['unread', 'read']),
          provider: provider._id,
          products: notificationProducts,
          createdAt: faker.date.recent(30), // Within last 30 days
          updatedAt: faker.date.recent(15), // Updated within last 15 days
        };
        
        notifications.push(notification);
      }
    }
    
    // Generate system notifications (admin to users)
    const adminUser = await User.findOne({ role: 'admin' });
    if (adminUser) {
      const systemMessages = [
        'Welcome to Beepr! Your cannabis marketplace.',
        'New features available - check out our updated app.',
        'Important: Please verify your account for full access.',
        'System maintenance scheduled for this weekend.',
        'New payment methods now available.',
        'Update your preferences for better recommendations.',
        'Security update: Please review your account settings.',
        'Holiday hours: Check our updated delivery schedule.',
      ];
      
      // Send system notifications to random users
      const allUsers = [...customers.map(c => c.user), ...providers.map(p => p.user)];
      const randomUsers = faker.random.arrayElements(allUsers, Math.min(20, allUsers.length));
      
      for (const user of randomUsers) {
        const message = faker.random.arrayElement(systemMessages);
        
        const notification = {
          receiver: user._id,
          message: message,
          type: 'provider_to_customer', // Using this type for system messages
          status: faker.random.arrayElement(['unread', 'read']),
          provider: null,
          products: [],
          createdAt: faker.date.recent(7), // Within last week
          updatedAt: faker.date.recent(3), // Updated within last 3 days
        };
        
        notifications.push(notification);
      }
    }
    
    return notifications;
  } catch (error) {
    console.error('Error generating notifications:', error);
    throw error;
  }
};

/**
 * Seed notifications
 */
const seed = async () => {
  try {
    const notifications = await generateNotifications();
    await Notification.insertMany(notifications);
    console.log(`✅ Seeded ${notifications.length} notifications`);
  } catch (error) {
    console.error('❌ Error seeding notifications:', error);
    throw error;
  }
};

module.exports = {
  seed,
  generateNotifications,
  notificationTemplates,
};
