const Joi = require('joi');
const { objectId } = require('./custom.validation');

// Param validation (e.g., notificationId)
const params = {
  params: Joi.object({
    notificationId: Joi.string().required().custom(objectId),
  }),
};

// Validation for status update (read/unread)
const updateStatus = {
  params: Joi.object({
    notificationId: Joi.string().required().custom(objectId),
  }),
  body: Joi.object({
    status: Joi.string().valid('read', 'unread').required(),
  }),
};



module.exports = {
  params,
  updateStatus,
};
