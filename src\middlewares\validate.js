const Joi = require('joi');
const httpStatus = require('http-status');
const pick = require('../utils/pick');
const { sendError } = require('../utils/ApiResponse');

// Schema compilation cache to avoid recompiling schemas on every request
const schemaCache = new Map();
const CACHE_SIZE_LIMIT = 500; // Limit cache size to prevent memory issues

// Create a cache key from schema object
const createCacheKey = (schema) => {
  return JSON.stringify(schema);
};

// Get or create compiled schema
const getCompiledSchema = (schema) => {
  const cacheKey = createCacheKey(schema);

  if (schemaCache.has(cacheKey)) {
    return schemaCache.get(cacheKey);
  }

  // Compile schema with optimized preferences
  const compiledSchema = Joi.compile(schema)
    .prefs({
      errors: { label: 'key' },
      abortEarly: false,
      cache: true // Enable Jo<PERSON>'s internal caching
    });

  // Manage cache size
  if (schemaCache.size >= CACHE_SIZE_LIMIT) {
    // Remove oldest entry (FIFO)
    const firstKey = schemaCache.keys().next().value;
    schemaCache.delete(firstKey);
  }

  schemaCache.set(cacheKey, compiledSchema);
  return compiledSchema;
};

// Optimized error processing function
const processValidationError = (error) => {
  const data = {};
  let firstMessage = 'Validation error occurred.';

  if (error.details && error.details.length > 0) {
    firstMessage = error.details[0].message;

    // Use for loop for better performance than reduce
    for (let i = 0; i < error.details.length; i++) {
      const detail = error.details[i];
      const field = detail.path[detail.path.length - 1];
      data[field] = detail.message;
    }
  }

  return { data, firstMessage };
};

const validate = (schema) => {
  // Pre-compile schema outside the middleware function for better performance
  const validSchemaKeys = ['params', 'query', 'body'];
  const validSchema = pick(schema, validSchemaKeys);
  const compiledSchema = getCompiledSchema(validSchema);
  const objectKeys = Object.keys(validSchema);

  return async (req, res, next) => {
    try {
      // Use pre-computed keys for better performance
      const object = pick(req, objectKeys);

      // Use pre-compiled schema
      const { value, error } = await compiledSchema.validateAsync(object);

      if (error) {
        const { data, firstMessage } = processValidationError(error);
        return sendError(res, firstMessage, httpStatus.BAD_REQUEST, data);
      }

      Object.assign(req, value);
      next();
    } catch (error) {
      if (error instanceof Joi.ValidationError) {
        const { data, firstMessage } = processValidationError(error);
        return sendError(res, firstMessage, httpStatus.BAD_REQUEST, data);
      }
      console.error('Unexpected validation error:', error);
      return sendError(res, 'Internal server error', httpStatus.INTERNAL_SERVER_ERROR);
    }
  };
};

module.exports = validate;
