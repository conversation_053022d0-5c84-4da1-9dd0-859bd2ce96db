const faker = require('faker');
const { Product, Question, ProductUsages, Category, SubCategory } = require('../src/models');
const Option = require('../src/models/option.model');

/**
 * Generate product usages
 */
const generateProductUsages = async () => {
  try {
    const products = await Product.find();
    const categories = await Category.find();
    const subCategories = await SubCategory.find();
    const questions = await Question.find();
    const options = await Option.find();

    if (products.length === 0 || questions.length === 0 || options.length === 0) {
      throw new Error('Missing required data. Please seed products, questions, and options first.');
    }

    const productUsages = [];

    for (const product of products) {
      // Find category and subcategory by ID
      const category = categories.find(c => c._id.toString() === product.category.toString());
      const subCategory = subCategories.find(sc => sc._id.toString() === product.subCategory.toString());

      if (!category || !subCategory) {
        console.log(`Skipping product ${product.name} - missing category or subcategory`);
        continue;
      }

      const categoryName = category.name;
      const subCategoryName = subCategory.name;
      const productName = product.name.toLowerCase();
      
      // Determine appropriate question-option pairs based on product type
      const usagePairs = [];
      
      // Primary reason for use
      const reasonQuestion = questions.find(q => q.value.includes('primary reason'));
      if (reasonQuestion) {
        const reasonOptions = options.filter(o => o.question.toString() === reasonQuestion._id.toString());
        
        if (categoryName === 'CBD Products' || productName.includes('cbd') || subCategoryName.includes('CBD')) {
          // CBD products - medical focused
          const medicalOptions = reasonOptions.filter(o => 
            ['Medical Treatment', 'Pain Relief', 'Anxiety Relief', 'Sleep Aid'].includes(o.value)
          );
          if (medicalOptions.length > 0) {
            usagePairs.push({
              question: reasonQuestion._id,
              option: faker.random.arrayElement(medicalOptions)._id,
            });
          }
        } else if (productName.includes('indica') || subCategoryName === 'Indica Strains') {
          // Indica - relaxation focused
          const relaxationOptions = reasonOptions.filter(o => 
            ['Pain Relief', 'Sleep Aid', 'Anxiety Relief', 'Recreational Use'].includes(o.value)
          );
          if (relaxationOptions.length > 0) {
            usagePairs.push({
              question: reasonQuestion._id,
              option: faker.random.arrayElement(relaxationOptions)._id,
            });
          }
        } else if (productName.includes('sativa') || subCategoryName === 'Sativa Strains') {
          // Sativa - energy focused
          const energyOptions = reasonOptions.filter(o => 
            ['Creative Enhancement', 'Social Use', 'Recreational Use'].includes(o.value)
          );
          if (energyOptions.length > 0) {
            usagePairs.push({
              question: reasonQuestion._id,
              option: faker.random.arrayElement(energyOptions)._id,
            });
          }
        } else {
          // Other products - random appropriate option
          const appropriateOptions = reasonOptions.filter(o => 
            !['Appetite Stimulation'].includes(o.value) || faker.datatype.boolean()
          );
          if (appropriateOptions.length > 0) {
            usagePairs.push({
              question: reasonQuestion._id,
              option: faker.random.arrayElement(appropriateOptions)._id,
            });
          }
        }
      }
      
      // Time of day
      const timeQuestion = questions.find(q => q.value.includes('time of day'));
      if (timeQuestion) {
        const timeOptions = options.filter(o => o.question.toString() === timeQuestion._id.toString());
        
        if (productName.includes('indica') || subCategoryName === 'Indica Strains' || productName.includes('sleep')) {
          // Indica/sleep products - evening/night
          const eveningOptions = timeOptions.filter(o => 
            ['Evening', 'Night', 'Before Bed'].includes(o.value)
          );
          if (eveningOptions.length > 0) {
            usagePairs.push({
              question: timeQuestion._id,
              option: faker.random.arrayElement(eveningOptions)._id,
            });
          }
        } else if (productName.includes('sativa') || subCategoryName === 'Sativa Strains') {
          // Sativa - morning/afternoon
          const dayOptions = timeOptions.filter(o => 
            ['Morning', 'Afternoon', 'After Work'].includes(o.value)
          );
          if (dayOptions.length > 0) {
            usagePairs.push({
              question: timeQuestion._id,
              option: faker.random.arrayElement(dayOptions)._id,
            });
          }
        } else {
          // Other products - random time
          if (timeOptions.length > 0) {
            usagePairs.push({
              question: timeQuestion._id,
              option: faker.random.arrayElement(timeOptions)._id,
            });
          }
        }
      }
      
      // Effects
      const effectsQuestion = questions.find(q => q.value.includes('effects are you looking for'));
      if (effectsQuestion) {
        const effectOptions = options.filter(o => o.question.toString() === effectsQuestion._id.toString());
        
        if (productName.includes('indica') || subCategoryName === 'Indica Strains') {
          // Indica effects
          const indicaEffects = effectOptions.filter(o => 
            ['Relaxation', 'Pain Relief', 'Sleep'].includes(o.value)
          );
          if (indicaEffects.length > 0) {
            // Add 1-2 effects
            const selectedEffects = faker.random.arrayElements(indicaEffects, faker.datatype.number({ min: 1, max: 2 }));
            selectedEffects.forEach(effect => {
              usagePairs.push({
                question: effectsQuestion._id,
                option: effect._id,
              });
            });
          }
        } else if (productName.includes('sativa') || subCategoryName === 'Sativa Strains') {
          // Sativa effects
          const sativaEffects = effectOptions.filter(o => 
            ['Energy Boost', 'Focus', 'Creativity', 'Euphoria', 'Mood Enhancement'].includes(o.value)
          );
          if (sativaEffects.length > 0) {
            const selectedEffects = faker.random.arrayElements(sativaEffects, faker.datatype.number({ min: 1, max: 2 }));
            selectedEffects.forEach(effect => {
              usagePairs.push({
                question: effectsQuestion._id,
                option: effect._id,
              });
            });
          }
        } else if (categoryName === 'CBD Products') {
          // CBD effects
          const cbdEffects = effectOptions.filter(o => 
            ['Relaxation', 'Pain Relief', 'Focus'].includes(o.value)
          );
          if (cbdEffects.length > 0) {
            const selectedEffects = faker.random.arrayElements(cbdEffects, faker.datatype.number({ min: 1, max: 2 }));
            selectedEffects.forEach(effect => {
              usagePairs.push({
                question: effectsQuestion._id,
                option: effect._id,
              });
            });
          }
        } else {
          // Random effects for other products
          if (effectOptions.length > 0) {
            const selectedEffects = faker.random.arrayElements(effectOptions, faker.datatype.number({ min: 1, max: 3 }));
            selectedEffects.forEach(effect => {
              usagePairs.push({
                question: effectsQuestion._id,
                option: effect._id,
              });
            });
          }
        }
      }
      
      // Consumption method
      const methodQuestion = questions.find(q => q.value.includes('consumption method'));
      if (methodQuestion) {
        const methodOptions = options.filter(o => o.question.toString() === methodQuestion._id.toString());
        
        if (categoryName === 'Cannabis Flower') {
          const flowerMethods = methodOptions.filter(o => 
            ['Smoking', 'Vaping'].includes(o.value)
          );
          if (flowerMethods.length > 0) {
            usagePairs.push({
              question: methodQuestion._id,
              option: faker.random.arrayElement(flowerMethods)._id,
            });
          }
        } else if (categoryName === 'Edibles') {
          const edibleMethod = methodOptions.find(o => o.value === 'Edibles');
          if (edibleMethod) {
            usagePairs.push({
              question: methodQuestion._id,
              option: edibleMethod._id,
            });
          }
        } else if (categoryName === 'Vape Products') {
          const vapeMethod = methodOptions.find(o => o.value === 'Vaping');
          if (vapeMethod) {
            usagePairs.push({
              question: methodQuestion._id,
              option: vapeMethod._id,
            });
          }
        } else if (categoryName === 'Cannabis Concentrates') {
          const concentrateMethods = methodOptions.filter(o => 
            ['Dabbing', 'Vaping'].includes(o.value)
          );
          if (concentrateMethods.length > 0) {
            usagePairs.push({
              question: methodQuestion._id,
              option: faker.random.arrayElement(concentrateMethods)._id,
            });
          }
        } else if (categoryName === 'Topicals') {
          const topicalMethod = methodOptions.find(o => o.value === 'Topicals');
          if (topicalMethod) {
            usagePairs.push({
              question: methodQuestion._id,
              option: topicalMethod._id,
            });
          }
        }
      }
      
      // Add medical conditions for medical-focused products
      if (categoryName === 'CBD Products' || productName.includes('medical') || productName.includes('cbd')) {
        const medicalQuestion = questions.find(q => q.value.includes('medical conditions'));
        if (medicalQuestion) {
          const medicalOptions = options.filter(o => o.question.toString() === medicalQuestion._id.toString());
          if (medicalOptions.length > 0) {
            const selectedConditions = faker.random.arrayElements(medicalOptions, faker.datatype.number({ min: 1, max: 2 }));
            selectedConditions.forEach(condition => {
              usagePairs.push({
                question: medicalQuestion._id,
                option: condition._id,
              });
            });
          }
        }
      }
      
      // Create ProductUsages entries
      for (const pair of usagePairs) {
        productUsages.push({
          product: product._id,
          question: pair.question,
          option: pair.option,
        });
      }
    }
    
    return productUsages;
  } catch (error) {
    console.error('Error generating product usages:', error);
    throw error;
  }
};

/**
 * Seed product usages
 */
const seed = async () => {
  try {
    const productUsages = await generateProductUsages();
    await ProductUsages.insertMany(productUsages);
    console.log(`✅ Seeded ${productUsages.length} product usages`);
  } catch (error) {
    console.error('❌ Error seeding product usages:', error);
    throw error;
  }
};

module.exports = {
  seed,
  generateProductUsages,
};
