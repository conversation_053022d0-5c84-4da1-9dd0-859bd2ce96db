const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { sendError } = require('../utils/ApiResponse');

const fileUpload = (directory, fieldsConfig = []) => {
  const uploadDirectory = path.join(__dirname, '..', directory);

  if (!fs.existsSync(uploadDirectory)) {
    fs.mkdirSync(uploadDirectory, { recursive: true });
  }

  const storage = multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, uploadDirectory);
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
      const fileExtension = path.extname(file.originalname);
      cb(null, uniqueSuffix + fileExtension);
    },
  });

  const upload = multer({
    storage,
    fileFilter: (req, file, cb) => {
      const fieldConfig = fieldsConfig.find(
        (field) => field.fieldName === file.fieldname || `${field.fieldName}[]` === file.fieldname
      );
      if (!fieldConfig) {
        return cb(new Error(`Unexpected field: ${file.fieldname}`), false);
      }

      const { allowedTypes } = fieldConfig;
      if (allowedTypes && !allowedTypes.includes(file.mimetype)) {
        return cb(new Error(`Invalid file type for ${file.fieldname}. Allowed types: ${allowedTypes.join(', ')}`), false);
      }
      cb(null, true);
    },
    limits: {
      fileSize: Math.max(...fieldsConfig.map((field) => field.maxSize || Infinity)),
    },
  });

  return (req, res, next) => {
    const uploadFields = fieldsConfig.map(({ fieldName, multiple }) => ({
      name: multiple ? `${fieldName}[]` : fieldName,
      maxCount: multiple ? undefined : 1,
    }));

    upload.fields(uploadFields)(req, res, (err) => {
      if (err instanceof multer.MulterError || err) {
        return sendError(res, err.message, 400);
      }
      if (err) {
        return sendError(res, err.message, 400);
      }

      for (const { fieldName, fileRequired, multiple } of fieldsConfig) {
        const effectiveFieldName = multiple ? `${fieldName}[]` : fieldName;

        if (fileRequired && (!req.files || !req.files[effectiveFieldName])) {
          return sendError(res, `File is required for ${effectiveFieldName}`, 400);
        }

        if (req.files && req.files[effectiveFieldName]) {
          const files = req.files[effectiveFieldName].map((file) => {
            return `https://${req.get('host')}/${directory}/${file.filename}`;
          });

          req.body[fieldName] = multiple ? files : files[0];
        }
      }

      next(); // Proceed to the next middleware
    });
  };
};

module.exports = fileUpload;
