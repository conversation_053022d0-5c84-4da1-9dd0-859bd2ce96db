const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { authService, userService, tokenService, emailService } = require('../services');
// const Seller = require('../models/seller.model');
const Message = require('../models/message.model');
const { sendSuccess, sendError } = require('../utils/ApiResponse');
const { findByColumnName } = require('../services/GlobalService');
const backgroundEmailService = require('../services/backgroundEmail.service');

const login = catchAsync(async (req, res) => {
  const { email, password } = req.body;
  const user = await authService.loginUserWithEmailAndPassword(email, password);
  const tokens = await tokenService.generateAuthTokens(user);
  if (user.role == 'customer') {
    const customer = await findByColumnName('Customer', { user: user._id });
    return sendSuccess(res, 'Login successful!', httpStatus.OK, { user, tokens, customer });
  } else if (user.role == 'provider') {
    const provider = await findByColumnName('Provider', { user: user._id }) ?? {};
    return sendSuccess(res, 'Login successful!', httpStatus.OK, { user, tokens, provider });
  }
  sendSuccess(res, 'Login successful!', httpStatus.OK, { user, tokens });
});

const logout = catchAsync(async (req, res) => {
  await authService.logout(req.body.refreshToken);
  sendSuccess(res, 'Logout successful!', httpStatus.NO_CONTENT);
});

const refreshTokens = catchAsync(async (req, res) => {
  const tokens = await authService.refreshAuth(req.body.refreshToken);
  sendSuccess(res, 'Tokens refreshed successfully!', httpStatus.OK, tokens);
});

const register = catchAsync(async (req, res) => {
  const user = await userService.createUser(req.body);
  const tokens = await tokenService.generateAuthTokens(user);

  // Queue welcome email in background
  try {
    const jobId = backgroundEmailService.queueUserRegistrationEmail(
      user.email,
      user.name || 'User',
      user.role
    );
    console.log(`Welcome email queued (Job ID: ${jobId}) for ${user.email}`);
  } catch (emailError) {
    console.error('Failed to queue welcome email:', emailError);
    // Don't fail registration if email queue fails
  }

  sendSuccess(res, 'Registration successful!', httpStatus.CREATED, { user, tokens });
});

const forgotPassword = catchAsync(async (req, res) => {
  const resetPasswordToken = await tokenService.generateResetPasswordToken(res, req.body.email);
  await emailService.sendResetPasswordEmail(req.body.email, resetPasswordToken);
  sendSuccess(res, 'Reset password process OTP sent on mail successfully!', httpStatus.OK);
});

const resetPassword = catchAsync(async (req, res) => {
  await authService.resetPassword(req.body.otp, req.body.email, req.body.password);
  sendSuccess(res, 'Password reset successful!', httpStatus.OK);
});

const sendVerificationEmail = catchAsync(async (req, res) => {
  const verifyEmailToken = await tokenService.generateVerifyEmailToken(req.user);
  await emailService.sendVerificationEmail(req.user.email, verifyEmailToken);
  sendSuccess(res, 'Email verification OTP sent on mail successfully!', httpStatus.OK);
});

const verifyEmail = catchAsync(async (req, res) => {
  await authService.verifyEmail(req.body.otp, req.user.email);
  sendSuccess(res, 'User email verified successfully!', httpStatus.OK);
});

const getUserFromToken = catchAsync(async (req, res) => {
  if (req.user.role === 'seller') {
    // const seller = await Seller.findOne({ userId: req.user._id });
    // if (!seller) {
    //   return sendSuccess(res, 'User fetched successfully!', httpStatus.OK, req.user);
    // } else {
    const message = await Message.findOne({ sellerId: seller._id });
    const responseData = message
      ? { ...req.user._doc, ...seller._doc, ...message._doc }
      : { ...req.user._doc, ...seller._doc };
    return sendSuccess(res, 'User fetched successfully!', httpStatus.OK, responseData);
    // }
  }
  sendSuccess(res, 'User fetched successfully!', httpStatus.OK, req.user);
});

module.exports = {
  register,
  login,
  logout,
  refreshTokens,
  forgotPassword,
  resetPassword,
  sendVerificationEmail,
  getUserFromToken,
  verifyEmail,
};
