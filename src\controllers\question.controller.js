const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const GlobalService = require('../services/GlobalService');
const PreferenceService = require('../services/PreferenceService');
const pick = require('../utils/pick');
const { sendSuccess, sendError } = require('../utils/ApiResponse');

const modelName = 'Question';
const resourceName = 'Question';
const resourcesName = 'Questions';
const uniqueAttribs = ['value', 'sequence'];
const aggregation = [
  {
    $lookup: {
      from: 'options',
      localField: '_id',
      foreignField: 'question',
      as: 'options',
      pipeline: [
        { $match: { deleted: { $ne: true } } },
        { $project: { __v: 0, deleted: 0,question: 0, createdAt:0, updatedAt:0  } }
      ],
    },
  },
  {
    $sort: { sequence: 1 },
  },
];

// Create a new question
const create = catchAsync(async (req, res) => {
  let question = await GlobalService.create(modelName, pick(req.body, ['value', 'sequence']));
  let result = {};
  let options = [];
  if (Array.isArray(req.body.options)) {
    for (const optionItem of req.body.options) {
      optionItem['question'] = question._id;
      const createdOptionItem = await GlobalService.create('Option', optionItem); // Use a different variable
      options.push(createdOptionItem);
    }
  }
  result = { ...question._doc, options };
  sendSuccess(res, `${resourceName} created successfully!`, httpStatus.CREATED, result);
});

// Get all questions
const index = catchAsync(async (req, res) => {
  const options = req.queryOptions || pick(req.query, ['sortBy', 'limit', 'page']);
  const searchFilter = req.searchFilter;
  const question = await GlobalService.getAll(modelName, options, aggregation, searchFilter);
  sendSuccess(res, `${resourcesName} fetched successfully!`, httpStatus.OK, question);
});

// Get a particular question
const view = catchAsync(async (req, res) => {
  const question = await GlobalService.getById(modelName, req.params.question, aggregation);
  if (!question) sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  sendSuccess(res, `${resourceName} fetched successfully!`, httpStatus.OK, question);
});

// Update a particular question with options
const update = catchAsync(async (req, res) => {
  const questionId = req.body.question;
  let question = await GlobalService.getById(modelName, questionId, aggregation);
  if (!question) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);

  const updatedOptions = req.body.options || [];
  let options = []; // To store the updated options

  // Step 1: Remove options that are not in the update list
  const optionIdsToRemove = question.options.filter(
    (option) => !updatedOptions.some((updated) => updated._id && updated._id.toString() === option._id.toString())
  );

  for (const optionToRemove of optionIdsToRemove) {
    await GlobalService.softDeleteById('Option', uniqueAttribs, optionToRemove._id);
  }

  // Step 2: Add new options or update existing ones
  let count = 0;
  for (const newOption of updatedOptions) {
    if (!newOption._id) {
      // If no _id, this is a new subquestion to create
      newOption['question'] = questionId;
      const createdOption = await GlobalService.create('Option', newOption);
      options.push(createdOption); // Add created subquestion to the list
    } else {
      const existingOption = question.options.find((option) => option._id.toString() === newOption._id.toString());

      if (existingOption) {
        // Update existing subquestion
        const updatedOption = await GlobalService.updateById('Option', newOption._id, newOption);
        options.push(updatedOption); // Add updated subquestion to the list
      }
    }
    count++;
  }

  // Step 3: Update the question itself (only if needed)
  question = await GlobalService.updateById(modelName, questionId, pick(req.body, ['value', 'sequence']), aggregation);

  sendSuccess(res, `${resourceName} updated successfully!`, httpStatus.OK, question);
});

// Soft delete a particular question
const softDelete = catchAsync(async (req, res) => {
  const question = await GlobalService.getById(modelName, req.body.question, aggregation);
  if (!question) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  for (const optionItem of question.options) {
    await GlobalService.softDeleteById('Option', uniqueAttribs, optionItem._id);
  }
  await GlobalService.softDeleteById(modelName, uniqueAttribs, req.body.question);
  sendSuccess(res, `${resourceName} deleted successfully!`, httpStatus.NO_CONTENT);
});

// Get all active questions and active sub questions without pagination
const fetch = catchAsync(async (req, res) => {
  const category = await GlobalService.getAllWithoutPagination(modelName, aggregation);
  sendSuccess(res, `${resourcesName} fetched successfully!`, httpStatus.OK, category);
});

const preference = catchAsync(async (req, res) => {
  let params = { question: req.body.question, user: req.user._id };
  let data = { ...req.body, user: req.user._id };
  await GlobalService.createOrUpdate('UserPreference', params, data);
  sendSuccess(res, `User preference upserted successfully!`, httpStatus.CREATED, []);
});

// Get user's current preferences with question and option details
const getUserPreferences = catchAsync(async (req, res) => {
  const userId = req.user._id;

  const userPreferences = await PreferenceService.getUserPreferencesWithDetails(userId);
  const completionStatus = await PreferenceService.checkPreferenceCompletion(userId);

  const result = {
    preferences: userPreferences,
    completionStatus
  };

  sendSuccess(res, 'User preferences fetched successfully!', httpStatus.OK, result);
});

// Get user's preference for a specific question
const getUserPreferenceByQuestion = catchAsync(async (req, res) => {
  const userId = req.user._id;
  const questionId = req.params.questionId;



  const userPreference = await PreferenceService.getUserPreferenceByQuestion(userId, questionId);



  if (!userPreference) {
    sendError(res, 'No preference found for this question', httpStatus.NOT_FOUND);
    return;
  }

  sendSuccess(res, 'User preference fetched successfully!', httpStatus.OK, userPreference);
});

// Create a new option
const optionsCreate = catchAsync(async (req, res) => {
  let options = [];
  if (Array.isArray(req.body.options)) {
    for (const optionItem of req.body.options) {
      optionItem['question'] = req.body.question;
      const createdOptionItem = await GlobalService.create('Option', optionItem); // Use a different variable
      options.push(createdOptionItem);
    }
  }
  sendSuccess(res, `${resourceName} created successfully!`, httpStatus.CREATED, options);
});

const optionUpdate = catchAsync(async (req, res) => {
  const option = await GlobalService.updateById('Option', req.body.option, pick(req.body, ['value']));
  sendSuccess(res, `Option updated successfully!`, httpStatus.OK, option);
});

// Soft delete a particular sub question
const optionSoftDelete = catchAsync(async (req, res) => {
  await GlobalService.softDeleteById('Option', uniqueAttribs, req.body.option);
  sendSuccess(res, `Option deleted successfully!`, httpStatus.NO_CONTENT);
});

module.exports = {
  create,
  index,
  fetch,
  view,
  update,
  softDelete,
  fetch,
  preference,
  getUserPreferences,
  getUserPreferenceByQuestion,
  optionsCreate,
  optionUpdate,
  optionSoftDelete,
};
