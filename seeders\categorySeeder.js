const faker = require('faker');
const { Category, SubCategory } = require('../src/models');

/**
 * Cannabis/Drug categories and subcategories data
 */
const categoriesData = [
  {
    name: 'Cannabis Flower',
    description: 'Premium cannabis flower products',
    image: 'https://example.com/images/cannabis-flower.jpg',
    subCategories: [
      'Indica Strains',
      'Sativa Strains',
      'Hybrid Strains',
      'High CBD Strains',
      'Pre-Rolls',
      'Shake & Trim',
    ],
  },
  {
    name: 'Cannabis Concentrates',
    description: 'High-potency cannabis extracts and concentrates',
    image: 'https://example.com/images/concentrates.jpg',
    subCategories: [
      'Shatter',
      'Wax',
      'Live Resin',
      'Rosin',
      'Hash',
      'Distillate',
      'Diamonds',
      'Sauce',
    ],
  },
  {
    name: 'Edibles',
    description: 'Cannabis-infused food and beverages',
    image: 'https://example.com/images/edibles.jpg',
    subCategories: [
      'Gummies',
      'Chocolates',
      'Cookies',
      'Beverages',
      'Capsules',
      'Tinctures',
      'Hard Candies',
      'Baked Goods',
    ],
  },
  {
    name: 'Vape Products',
    description: 'Vaporizers and vape cartridges',
    image: 'https://example.com/images/vape.jpg',
    subCategories: [
      'Vape Cartridges',
      'Disposable Vapes',
      'Vaporizers',
      'Vape Batteries',
      'Pod Systems',
    ],
  },
  {
    name: 'Topicals',
    description: 'Cannabis-infused topical products',
    image: 'https://example.com/images/topicals.jpg',
    subCategories: [
      'Balms',
      'Lotions',
      'Salves',
      'Bath Products',
      'Patches',
      'Massage Oils',
    ],
  },
  {
    name: 'CBD Products',
    description: 'CBD-focused wellness products',
    image: 'https://example.com/images/cbd.jpg',
    subCategories: [
      'CBD Oil',
      'CBD Gummies',
      'CBD Capsules',
      'CBD Topicals',
      'CBD Pet Products',
      'CBD Isolate',
    ],
  },
  {
    name: 'Accessories',
    description: 'Cannabis consumption accessories',
    image: 'https://example.com/images/accessories.jpg',
    subCategories: [
      'Pipes',
      'Bongs',
      'Rolling Papers',
      'Grinders',
      'Storage',
      'Lighters',
      'Cleaning Supplies',
    ],
  },
  {
    name: 'Seeds & Clones',
    description: 'Cannabis seeds and clones for cultivation',
    image: 'https://example.com/images/seeds.jpg',
    subCategories: [
      'Feminized Seeds',
      'Auto-flowering Seeds',
      'Regular Seeds',
      'Clones',
      'Mother Plants',
    ],
  },
];

/**
 * Seed categories and subcategories
 */
const seed = async () => {
  try {
    const categories = [];
    const subCategories = [];
    
    for (const categoryData of categoriesData) {
      // Create category
      const category = new Category({
        name: categoryData.name,
        description: categoryData.description,
        image: categoryData.image,
        status: true,
      });
      
      const savedCategory = await category.save();
      categories.push(savedCategory);
      
      // Create subcategories for this category
      for (const subCatName of categoryData.subCategories) {
        const subCategory = new SubCategory({
          category: savedCategory._id,
          name: subCatName,
          status: true,
        });
        
        const savedSubCategory = await subCategory.save();
        subCategories.push(savedSubCategory);
      }
    }
    
    console.log(`✅ Seeded ${categories.length} categories and ${subCategories.length} subcategories`);
  } catch (error) {
    console.error('❌ Error seeding categories:', error);
    throw error;
  }
};

module.exports = {
  seed,
  categoriesData,
};
