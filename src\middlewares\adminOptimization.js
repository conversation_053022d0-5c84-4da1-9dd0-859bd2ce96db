/**
 * Admin route optimization middleware
 * Provides common optimizations for admin GET routes
 */

/**
 * Streamlined admin route optimization middleware
 * - Processes query parameters efficiently
 * - Eliminates redundant processing
 * - Provides instant response optimization
 */
const adminRouteOptimization = (req, res, next) => {
  // Set response headers for faster loading
  res.set({
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'SAMEORIGIN',
    'X-XSS-Protection': '1; mode=block'
  });

  // Quick optimization without heavy processing
  if (req.queryOptions) {
    // Fast parameter optimization
    const limit = parseInt(req.query.limit) || 10;
    const page = parseInt(req.query.page) || 1;

    req.queryOptions.limit = Math.min(limit, 50); // Reduced max for faster loading
    req.queryOptions.page = Math.max(page, 1);

    // Only set default sorting if no sorting is specified and no custom aggregation is being used
    if (!req.queryOptions.sortBy && !req.query.sortBy) {
      // Use a more reliable default sort that works with aggregation
      req.queryOptions.sortBy = '_id:desc'; // _id is always available and indexed
    }
  }

  // Set admin flag for fast processing
  req.isAdminRoute = true;

  next();
};

/**
 * Model-specific search field configurations for admin routes
 */
const getSearchFieldsForModel = (modelName) => {
  const searchConfigs = {
    'Category': ['name', 'description'],
    'Question': ['value'],
    'Customer': ['username', 'bio', 'email'], // Search by username, bio, and email from aggregation
    'Provider': ['name', 'description', 'city', 'state', 'email'], // Search by name, description, city, state, and email from aggregation
    'Product': [], // Product search handled by custom aggregation
    'Order': [], // Order search handled by custom aggregation
    'Review': ['title', 'experience'],
    'Notification': ['title', 'message']
  };

  return searchConfigs[modelName] || [];
};

/**
 * Create streamlined query filter for admin routes
 * Combines query processing with optimization for faster loading
 */
const createAdminQueryFilter = (modelName) => {
  const pick = require('../utils/pick');

  return (req, res, next) => {
    // Fast query parameter extraction
    const standardOptions = pick(req.query, ['sortBy', 'limit', 'page']);

    // Quick search processing - only if search is provided
    let searchFilter;
    const { search } = req.query;

    if (search && search.trim()) {
      const searchFields = getSearchFieldsForModel(modelName);
      if (searchFields.length > 0) {
        const searchRegex = { $regex: search.trim(), $options: 'i' };
        if (searchFields.length === 1) {
          searchFilter = { [searchFields[0]]: searchRegex };
        } else {
          searchFilter = { $or: searchFields.map(field => ({ [field]: searchRegex })) };
        }
      }
    }

    // Attach to request for fast access
    req.queryOptions = standardOptions;
    req.searchFilter = searchFilter;
    req.hasSearch = !!searchFilter;

    // Immediate optimization
    adminRouteOptimization(req, res, next);
  };
};

module.exports = {
  adminRouteOptimization,
  createAdminQueryFilter,
  getSearchFieldsForModel
};
