const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins'); // Assuming you already have these utilities

const favouriteSchema = mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    variantId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Variant',
      required: true,
    },
    isLiked: {
      type: Boolean,
      default: true, // Defaults to liked (or set as per your requirement)
    },
  },
  {
    timestamps: true, // Adds createdAt and updatedAt fields automatically
  }
);

// Plugins (for soft delete, pagination, and JSON conversion)
favouriteSchema.plugin(toJSON);

favouriteSchema.plugin(paginate);

const Favourite = mongoose.model('Favourites', favouriteSchema);

module.exports = Favourite;
