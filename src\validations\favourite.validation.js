const Joi = require('joi');
const { User, Variant } = require('../models');
const { exists, objectId } = require('./custom.validation');

const userId = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: User, field: '_id', value });
    return value;
  });

const variantId = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: Variant, field: '_id', value });
    return value;
  });

const createFavourite = {
  body: Joi.object({
    userId,
    variantId,
  }),
};

const toggleLike = {
  body: Joi.object({
    userId,
    variantId,
  }),
};

module.exports = {
  createFavourite,
  toggleLike,
};
