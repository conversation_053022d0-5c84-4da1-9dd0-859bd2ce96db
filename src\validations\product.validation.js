const Joi = require('joi');
const { Product, Variant, Category, SubCategory, Provider, Question, Option, ProductUsages } = require('../models');
const { exists, unique, objectId } = require('./custom.validation');

const product = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: Product, field: '_id', value });
    return value;
  });
const provider = Joi.optional()
  .external(async (value, helpers) => {
    if (value) {
      objectId(value, helpers);
      await exists({ model: Provider, field: '_id', value });
    }
    return value;
  });
const variant = Joi.optional().external(async (value, helpers) => {
  if (value) {
    objectId(value, helpers);
    await exists({ model: Variant, field: '_id', value });
  }
  return value;
});

const productUsage = Joi.optional().external(async (value, helpers) => {
  if (value) {
    objectId(value, helpers);
    await exists({ model: ProductUsages, field: '_id', value });
  }
  return value;
});

const category = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: Category, field: '_id', value });
    return value;
  });

const subCategory = Joi.string()
  .custom(objectId)
  .required()
  .external(async (value) => {
    await exists({ model: SubCategory, field: '_id', value });
    return value;
  });
const question = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: Question, field: '_id', value });
    return value;
  });

const option = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: Option, field: '_id', value });
    return value;
  });

const params = {
  params: Joi.object({
    product,
  }),
};

const body = {
  body: Joi.object({
    product,
  }),
};

const create = {
  body: Joi.object({
    provider,
    name: Joi.string().required().max(255),
    description: Joi.string().required().max(1000),
    category,
    subCategory,
    delivery: Joi.boolean(),
    minDeliveryAmount: Joi.number().required(),
    shippingCharges:Joi.number().required(),
    photo: Joi.array().items(Joi.string().uri().required()).min(1),
    variants: Joi.array()
      .items(
        Joi.object({
          amount: Joi.number().required(),
          unit: Joi.string().valid('Gram', 'Kilogram', 'Milliliter', 'Liter', 'Piece').required(),
          default: Joi.boolean(),
          fake_price: Joi.number().required(),
          price: Joi.number().required(),
          flavour: Joi.string().optional(),
          stock: Joi.number().required(),
        })
      )
      .min(1),
    productUsages: Joi.array().items(
      Joi.object({
        question,
        option,
      })
    ),
  }),
};

const update = {
  body: Joi.object({
    provider,
    product,
    name: Joi.string().required().max(255),
    description: Joi.string().required().max(1000),
    category,
    subCategory,
    delivery: Joi.boolean(),
    minDeliveryAmount: Joi.number().required(),
    shippingCharges: Joi.number().required(),
    variants: Joi.array()
      .items(
        Joi.object({
          _id: variant,
          amount: Joi.number().required(),
          unit: Joi.string().valid('Gram', 'Kilogram', 'Milliliter', 'Liter', 'Piece').required(),
          default: Joi.boolean(),
          fake_price: Joi.number().required(),
          price: Joi.number().required(),
          flavour: Joi.string().optional(),
          stock: Joi.number().required(),
        })
      )
      .min(1)
      .required(),
    photo: Joi.array().items(Joi.string().uri().required()).optional(),
    productUsages: Joi.array()
      .items(
        Joi.object({
          _id: productUsage,
          question,
          option,
        })
      )
      .min(1)
      .required(),
  }),
};

module.exports = { params, body, create, update };
