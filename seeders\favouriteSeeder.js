const faker = require('faker');
const { User, Customer, Variant, Favourite } = require('../src/models');

/**
 * Generate favourites
 */
const generateFavourites = async () => {
  try {
    const customers = await Customer.find().populate('user');
    const variants = await Variant.find();
    
    if (customers.length === 0 || variants.length === 0) {
      throw new Error('Missing required data. Please seed customers and variants first.');
    }
    
    const favourites = [];
    const existingFavourites = new Set(); // To avoid duplicates
    
    for (const customer of customers) {
      // Each customer has 0-10 favourite products
      const numFavourites = faker.datatype.number({ min: 0, max: 10 });
      
      // Get random variants for this customer
      const shuffledVariants = faker.helpers.shuffle([...variants]);
      
      for (let i = 0; i < numFavourites && i < shuffledVariants.length; i++) {
        const variant = shuffledVariants[i];
        const favouriteKey = `${customer.user._id}-${variant._id}`;
        
        // Avoid duplicate favourites
        if (existingFavourites.has(favouriteKey)) {
          continue;
        }
        
        existingFavourites.add(favouriteKey);
        
        const favourite = {
          userId: customer.user._id,
          variantId: variant._id,
          isLiked: true, // All favourites are liked by default
          createdAt: faker.date.recent(90), // Favourited within last 3 months
          updatedAt: faker.date.recent(30), // Updated within last month
        };
        
        favourites.push(favourite);
      }
    }
    
    return favourites;
  } catch (error) {
    console.error('Error generating favourites:', error);
    throw error;
  }
};

/**
 * Seed favourites
 */
const seed = async () => {
  try {
    const favourites = await generateFavourites();
    await Favourite.insertMany(favourites);
    console.log(`✅ Seeded ${favourites.length} favourites`);
  } catch (error) {
    console.error('❌ Error seeding favourites:', error);
    throw error;
  }
};

module.exports = {
  seed,
  generateFavourites,
};
