const { Question } = require('../src/models');
const Option = require('../src/models/option.model');

/**
 * Questions and options for cannabis product usage
 */
const questionsData = [
  {
    value: 'What is your primary reason for using cannabis?',
    sequence: 1,
    options: [
      'Medical Treatment',
      'Pain Relief',
      'Anxiety Relief',
      'Sleep Aid',
      'Recreational Use',
      'Creative Enhancement',
      'Social Use',
      'Appetite Stimulation',
    ],
  },
  {
    value: 'What time of day do you prefer to use this product?',
    sequence: 2,
    options: [
      'Morning',
      'Afternoon',
      'Evening',
      'Night',
      'Anytime',
      'Before Meals',
      'After Work',
      'Before Bed',
    ],
  },
  {
    value: 'What effects are you looking for?',
    sequence: 3,
    options: [
      'Relaxation',
      'Energy Boost',
      'Focus',
      'Euphoria',
      'Pain Relief',
      'Sleep',
      'Creativity',
      'Appetite',
      'Mood Enhancement',
    ],
  },
  {
    value: 'What is your experience level with cannabis?',
    sequence: 4,
    options: [
      'Beginner',
      'Intermediate',
      'Advanced',
      'Expert',
      'Medical Patient',
      'Daily User',
      'Occasional User',
    ],
  },
  {
    value: 'What consumption method do you prefer?',
    sequence: 5,
    options: [
      'Smoking',
      'Vaping',
      'Edibles',
      'Topicals',
      'Tinctures',
      'Dabbing',
      'Capsules',
      'Sublingual',
    ],
  },
  {
    value: 'What potency level do you prefer?',
    sequence: 6,
    options: [
      'Low THC (0-10%)',
      'Medium THC (10-20%)',
      'High THC (20-30%)',
      'Very High THC (30%+)',
      'High CBD',
      'Balanced THC/CBD',
      'CBD Dominant',
    ],
  },
  {
    value: 'What medical conditions are you treating?',
    sequence: 7,
    options: [
      'Chronic Pain',
      'Anxiety',
      'Depression',
      'Insomnia',
      'PTSD',
      'Epilepsy',
      'Cancer',
      'Arthritis',
      'Migraines',
      'Nausea',
      'Glaucoma',
      'Multiple Sclerosis',
    ],
  },
  {
    value: 'What flavor profiles do you enjoy?',
    sequence: 8,
    options: [
      'Citrus',
      'Berry',
      'Pine',
      'Earthy',
      'Sweet',
      'Spicy',
      'Floral',
      'Diesel',
      'Fruity',
      'Herbal',
      'Minty',
      'Vanilla',
    ],
  },
  {
    value: 'How often do you use cannabis products?',
    sequence: 9,
    options: [
      'Daily',
      'Several times a week',
      'Weekly',
      'Monthly',
      'Occasionally',
      'As needed for symptoms',
      'First time',
    ],
  },
  {
    value: 'What is your budget range?',
    sequence: 10,
    options: [
      'Budget ($10-30)',
      'Mid-range ($30-60)',
      'Premium ($60-100)',
      'Luxury ($100+)',
      'Medical discount',
      'Bulk pricing',
    ],
  },
];

/**
 * Seed questions and options
 */
const seed = async () => {
  try {
    const questions = [];
    const options = [];
    
    for (const questionData of questionsData) {
      // Create question
      const question = new Question({
        value: questionData.value,
        sequence: questionData.sequence,
      });
      
      const savedQuestion = await question.save();
      questions.push(savedQuestion);
      
      // Create options for this question
      for (const optionValue of questionData.options) {
        const option = new Option({
          question: savedQuestion._id,
          value: optionValue,
        });
        
        const savedOption = await option.save();
        options.push(savedOption);
      }
    }
    
    console.log(`✅ Seeded ${questions.length} questions and ${options.length} options`);
  } catch (error) {
    console.error('❌ Error seeding questions:', error);
    throw error;
  }
};

module.exports = {
  seed,
  questionsData,
};
