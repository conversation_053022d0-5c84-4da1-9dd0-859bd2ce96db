const Joi = require('joi');
const moment = require('moment'); // Make sure you have moment.js installed

const objectId = (value, helpers) => {
  if (!value.match(/^[0-9a-fA-F]{24}$/)) {
    return helpers.message('"{{#label}}" must be a valid mongo id');
  }
  return value;
};

const password = (value, helpers) => {
  if (value.length < 8) {
    return helpers.message('password must be at least 8 characters');
  }
  if (!value.match(/\d/) || !value.match(/[a-zA-Z]/)) {
    return helpers.message('password must contain at least 1 letter and 1 number');
  }
  return value;
};
// Helper to create structured validation details
const createValidationDetails = (message, field) => [
  {
    message,
    path: [field],
    type: 'any.custom',
    context: { key: field, label: field },
  },
];

// Function to ensure a field value or combination of field values is unique
const unique = async ({ model, field, value, excludeId = null }) => {
  const query = {};

  if (Array.isArray(field)) {
    if (!Array.isArray(value) || field.length !== value.length) {
      const message = 'Field and value must be arrays of the same length';
      throw new Joi.ValidationError(message, createValidationDetails(message, 'field'), {});
    }
    field.forEach((fieldName, index) => {
      query[fieldName] = value[index];
    });
  } else {
    query[field] = value;
  }
  if (excludeId) {
    query._id = { $ne: excludeId };
  }

  const existingRecord = await model.findOne(query);
  if (existingRecord) {
    const message = Array.isArray(field)
      ? `${field.join(' & ')} combination already exists`
      : `${field} "${value}" already exists`;
    throw new Joi.ValidationError(message, createValidationDetails(message, field), {});
  }
  return value;
};

// Function to check if a record exists for the given field or combination of fields
const exists = async ({ model, field, value }) => {
  const query = {};

  if (!value)
    return value;

  if (Array.isArray(field)) {
    if (!Array.isArray(value) || field.length !== value.length) {
      const message = 'Field and value must be arrays of the same length';
      throw new Joi.ValidationError(message, createValidationDetails(message, 'field'), {});
    }
    field.forEach((fieldName, index) => {
      query[fieldName] = value[index];
    });
  } else {
    query[field] = value;
  }

  const existingRecord = await model.findOne(query);
  if (!existingRecord) {
    const message = Array.isArray(field) ? `${field.join(' & ')} combination not found` : `${field} "${value}" not found`;
    throw new Joi.ValidationError(message, createValidationDetails(message, field), {});
  }
  return value;
};

// Latitude Validator
const latitude = (value, helpers) => {
  const latitudeRegex = /^(-?([1-8]?[0-9](\.\d+)?|90(\.0+)?))$/;
  if (!latitudeRegex.test(value)) {
    return helpers.message(
      '"{{#label}}" must be a valid latitude between -90 and 90'
    );
  }
  return value;
};

// Longitude Validator
const longitude = (value, helpers) => {
  const longitudeRegex = /^(-?((1[0-7][0-9]|[1-9]?[0-9])(\.\d+)?|180(\.0+)?))$/;
  if (!longitudeRegex.test(value)) {
    return helpers.message(
      '"{{#label}}" must be a valid longitude between -180 and 180'
    );
  }
  return value;
};

// flexible validation function that can handle different formats like time, date, and datetime
const timeComparison = (value, helpers, comparisonValue, condition, timeFormat) => {
  // Use the provided time format for strict parsing
  const valueDateTime = moment(value, timeFormat, true); // Strict parsing with custom format
  const comparisonDateTime = moment(comparisonValue, timeFormat, true); // Strict parsing with custom format

  if (!valueDateTime.isValid() || !comparisonDateTime.isValid()) {
    return helpers.message(`Invalid time format. Expected format: ${timeFormat}`);
  }

  // Perform the condition check based on the provided condition
  switch (condition) {
    case 'greaterThan':
      if (valueDateTime.isBefore(comparisonDateTime)) {
        return helpers.message(`${value} must be greater than ${comparisonValue}`);
      }
      break;
    
    case 'greaterThanOrEqualTo':
      if (valueDateTime.isBefore(comparisonDateTime)) {
        return helpers.message(`${value} must be greater than or equal to ${comparisonValue}`);
      }
      break;

    case 'lessThan':
      if (valueDateTime.isAfter(comparisonDateTime)) {
        return helpers.message(`${value} must be less than ${comparisonValue}`);
      }
      break;

    case 'lessThanOrEqualTo':
      if (valueDateTime.isAfter(comparisonDateTime)) {
        return helpers.message(`${value} must be less than or equal to ${comparisonValue}`);
      }
      break;

    case 'equalTo':
      if (!valueDateTime.isSame(comparisonDateTime)) {
        return helpers.message(`${value} must be equal to ${comparisonValue}`);
      }
      break;

    default:
      return helpers.message('Invalid comparison condition');
  }

  return value; // Return the valid value
};


module.exports = { objectId, password, unique, exists, latitude, longitude, timeComparison };
