const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const paymentOptionSchema = mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    status: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

paymentOptionSchema.plugin(toJSON);
paymentOptionSchema.plugin(paginate);

const paymentOption = mongoose.model('paymentOption', paymentOptionSchema);

module.exports = paymentOption;