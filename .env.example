# Port number
PORT=3000

# URL of the Mongo DB
MONGODB_URL=mongodb+srv://ridhibordiya:<EMAIL>/beepr?retryWrites=true&w=majority&appName=Cluster0
# JWT
# JWT secret key
JWT_SECRET=thisisasamplesecret
# Number of minutes after which an access token expires
JWT_ACCESS_EXPIRATION_MINUTES=300
# Number of days after which a refresh token expires
JWT_REFRESH_EXPIRATION_DAYS=30
# Number of minutes after which a reset password token expires
JWT_RESET_PASSWORD_EXPIRATION_MINUTES=1
# Number of minutes after which a verify email token expires
# Number of minutes after which a verify email token expires
JWT_VERIFY_EMAIL_EXPIRATION_MINUTES=1

# SMTP configuration options for the email service
# For testing, you can use a fake SMTP service like Ethereal: https://ethereal.email/create
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USERNAME="<EMAIL>"
SMTP_PASSWORD="ucwchigekzykmpsr"
EMAIL_FROM="Darshil Jain"

FIREBASE_SENDER_ID=124892789819
FIREBASE_SERVER_KEY=BEZjB19Zi_ILMovj5qf6D5sIm4o8fj0yCfe_K5hxF2RuCjQkjkrbHHGFMwVgaQUy9wXkXBz43aImFBXeURnu1P8

