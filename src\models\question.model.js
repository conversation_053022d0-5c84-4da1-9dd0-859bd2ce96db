const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const MongooseDelete = require('mongoose-delete');

const questionSchema = mongoose.Schema(
  {
    value: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    sequence: {
      type: Number,
      default: null,
      required: false,
      unique: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add plugin that converts mongoose to json
questionSchema.plugin(toJSON);
questionSchema.plugin(paginate);
questionSchema.plugin(MongooseDelete, { overrideMethods: 'all' });

const Question = mongoose.model('Question', questionSchema);
module.exports = Question;
