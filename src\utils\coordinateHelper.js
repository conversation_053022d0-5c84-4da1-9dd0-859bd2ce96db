const httpStatus = require('http-status');
const ApiError = require('./ApiError');

/**
 * Helper function to convert latitude and longitude to GeoJSON Point format
 * @param {Object} body - Request body containing latitude and longitude
 * @param {boolean} strict - Whether to throw error if only one coordinate is provided (default: false)
 * @returns {Object} Modified body with location field
 */
const convertCoordinatesToLocation = (body, strict = false) => {
  if (body.latitude || body.longitude) {
    // Validate presence of both fields if strict mode or if any coordinate is provided
    if (strict && (!body.latitude || !body.longitude)) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Both latitude and longitude are required');
    }
    
    if (body.latitude && body.longitude) {
      // Parse and validate coordinates
      const longitude = parseFloat(body.longitude);
      const latitude = parseFloat(body.latitude);

      if (isNaN(longitude) || isNaN(latitude)) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid coordinate values');
      }

      // Validate coordinate ranges
      if (latitude < -90 || latitude > 90) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Latitude must be between -90 and 90');
      }
      
      if (longitude < -180 || longitude > 180) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Longitude must be between -180 and 180');
      }

      // Create GeoJSON point (longitude first, then latitude as per GeoJSON standard)
      body.location = {
        type: 'Point',
        coordinates: [longitude, latitude]
      };

      // Remove original latitude and longitude fields
      delete body.latitude;
      delete body.longitude;
    }
  }
  return body;
};

module.exports = {
  convertCoordinatesToLocation
};
