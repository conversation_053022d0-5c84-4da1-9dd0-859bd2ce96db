const Joi = require('joi');

/**
 * Common validation schemas for query parameters
 * These can be reused across different route validations
 */

// Base query parameters for pagination and sorting
const baseQuery = {
  // Pagination
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  
  // Sorting (existing format: field:asc or field:desc)
  sortBy: Joi.string().pattern(/^[a-zA-Z_][a-zA-Z0-9_.]*:(asc|desc)$/),
  
  // Alternative sorting (more intuitive: field:asc or field:desc or just field)
  orderBy: Joi.string().pattern(/^[a-zA-Z_][a-zA-Z0-9_.]*(:asc|:desc)?$/),
  
  // Search functionality
  search: Joi.string().trim().min(1).max(100),
  
  // Include deleted records (for admin purposes)
  includeDeleted: Joi.boolean().default(false)
};

// Standard query validation for list endpoints
const listQuery = {
  query: Joi.object().keys(baseQuery)
};

// Extended query validation with additional filters
const extendedQuery = (additionalFields = {}) => ({
  query: Joi.object().keys({
    ...baseQuery,
    ...additionalFields
  })
});

// Model-specific query validations
const categoryQuery = extendedQuery({
  status: Joi.string().valid('active', 'inactive')
});

const customerQuery = extendedQuery({
  isApproved: Joi.string().valid('pending', 'approved', 'rejected'),
  role: Joi.string().valid('customer', 'admin')
});

const providerQuery = extendedQuery({
  isApproved: Joi.string().valid('pending', 'approved', 'rejected'),
  city: Joi.string().trim(),
  state: Joi.string().trim(),
  country: Joi.string().trim()
});

const productQuery = extendedQuery({
  category: Joi.string().pattern(/^[0-9a-fA-F]{24}$/), // ObjectId pattern
  provider: Joi.string().pattern(/^[0-9a-fA-F]{24}$/), // ObjectId pattern
  minPrice: Joi.number().min(0),
  maxPrice: Joi.number().min(0),
  inStock: Joi.boolean()
});

const orderQuery = extendedQuery({
  status: Joi.string().valid('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'),
  user_id: Joi.string().pattern(/^[0-9a-fA-F]{24}$/), // ObjectId pattern
  provider_id: Joi.string().pattern(/^[0-9a-fA-F]{24}$/), // ObjectId pattern
  minAmount: Joi.number().min(0),
  maxAmount: Joi.number().min(0),
  dateFrom: Joi.date().iso(),
  dateTo: Joi.date().iso().min(Joi.ref('dateFrom'))
});

const reviewQuery = extendedQuery({
  rating: Joi.number().integer().min(1).max(5),
  userId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/), // ObjectId pattern
  providerId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/), // ObjectId pattern
  minRating: Joi.number().integer().min(1).max(5),
  maxRating: Joi.number().integer().min(1).max(5)
});

const notificationQuery = extendedQuery({
  isRead: Joi.boolean(),
  type: Joi.string().valid('info', 'warning', 'error', 'success'),
  userId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/), // ObjectId pattern
  dateFrom: Joi.date().iso(),
  dateTo: Joi.date().iso().min(Joi.ref('dateFrom'))
});

const questionQuery = extendedQuery({
  hasOptions: Joi.boolean()
});

// Helper function to create custom query validation
const createCustomQuery = (customFields = {}) => {
  return extendedQuery(customFields);
};

// Validation for search-specific endpoints
const searchQuery = {
  query: Joi.object().keys({
    search: Joi.string().required().trim().min(1).max(100),
    limit: Joi.number().integer().min(1).max(50).default(10),
    page: Joi.number().integer().min(1).default(1),
    sortBy: Joi.string().pattern(/^[a-zA-Z_][a-zA-Z0-9_.]*:(asc|desc)$/)
  })
};

module.exports = {
  baseQuery,
  listQuery,
  extendedQuery,
  categoryQuery,
  customerQuery,
  providerQuery,
  productQuery,
  orderQuery,
  reviewQuery,
  notificationQuery,
  questionQuery,
  searchQuery,
  createCustomQuery
};
