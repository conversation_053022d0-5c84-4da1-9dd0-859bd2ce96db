const nodemailer = require('nodemailer');
const config = require('../config/config');
const logger = require('../config/logger');

const transport = nodemailer.createTransport(config.email.smtp);
/* istanbul ignore next */
if (config.env !== 'test') {
  transport
    .verify()
    .then(() => logger.info('Connected to email server'))
    .catch(() => logger.warn('Unable to connect to email server. Make sure you have configured the SMTP options in .env'));
}

/**
 * Send an email
 * @param {string} to
 * @param {string} subject
 * @param {string} text
 * @returns {Promise}
 */
const sendEmail = async (to, subject, text) => {
  const msg = { from: config.email.from, to, subject, text };
  await transport.sendMail(msg);
};

/**
 * Send reset password email
 * @param {string} to
 * @param {string} token
 * @returns {Promise}
 */
const sendResetPasswordEmail = async (to, token) => {
  const subject = 'Reset password';
  // replace this url with the link to the reset password page of your front-end app
  const text = `Dear user,
    To reset your password, please use OTP: ${token}
    Thank you!`;
  await sendEmail(to, subject, text);
};

/**
 * Send verification email
 * @param {string} to
 * @param {string} token
 * @returns {Promise}
 */
const sendVerificationEmail = async (to, token) => {
  const subject = 'Email Verification';
  // replace this url with the link to the email verification page of your front-end app
  const text = `Dear user,
To verify your email, please use OTP: ${token}
Thank you!`;
  await sendEmail(to, subject, text);
};

/**
 * Send user registration welcome email
 * @param {string} to
 * @param {string} userName
 * @param {string} userRole
 * @returns {Promise}
 */
const sendUserRegistrationEmail = async (to, userName, userRole) => {
  const subject = 'Welcome to Beepr - Account Created Successfully!';

  let roleMessage = '';
  let nextSteps = '';

  if (userRole === 'provider') {
    roleMessage = 'You have successfully registered as a Provider on Beepr platform.';
    nextSteps = `Next Steps:
1. Complete your provider profile with business details
2. Upload required documents (Cannabis License, Reseller's Permit, Photo ID)
3. Wait for admin approval to start offering your services
4. Once approved, you can start listing your products and services`;
  } else if (userRole === 'customer') {
    roleMessage = 'You have successfully registered as a Customer on Beepr platform.';
    nextSteps = `Next Steps:
1. Complete your profile with personal details
2. Set your location preferences
3. Browse available providers and products in your area
4. Start placing orders from verified providers`;
  } else {
    roleMessage = 'You have successfully registered on Beepr platform.';
    nextSteps = 'Please complete your profile to get started.';
  }

  const text = `Dear ${userName},

Welcome to Beepr! 🎉

${roleMessage}

Your account has been created successfully and you can now access all the features available to ${userRole}s.

${nextSteps}

If you have any questions or need assistance, please don't hesitate to contact our support team.

Best regards,
Beepr Team

---
This is an automated message. Please do not reply to this email.`;

  await sendEmail(to, subject, text);
};

/**
 * Send provider status change notification email
 * @param {string} to
 * @param {string} providerName
 * @param {string} status
 * @returns {Promise}
 */
const sendProviderStatusChangeEmail = async (to, providerName, status) => {
  const subject = 'Provider Status Update - Beepr';

  let statusMessage = '';
  let statusColor = '';

  switch (status) {
    case 'approved':
      statusMessage = 'Congratulations! Your provider application has been approved. You can now start offering your services on our platform.';
      statusColor = 'green';
      break;
    case 'rejected':
      statusMessage = 'We regret to inform you that your provider application has been rejected. Please contact support for more information.';
      statusColor = 'red';
      break;
    case 'pending':
      statusMessage = 'Your provider application status has been changed to pending. We are currently reviewing your application.';
      statusColor = 'orange';
      break;
    default:
      statusMessage = `Your provider application status has been updated to: ${status}`;
      statusColor = 'blue';
  }

  const text = `Dear ${providerName},

${statusMessage}

Current Status: ${status.toUpperCase()}

If you have any questions, please don't hesitate to contact our support team.

Best regards,
Beepr Team`;

  await sendEmail(to, subject, text);
};

module.exports = {
  transport,
  sendEmail,
  sendResetPasswordEmail,
  sendVerificationEmail,
  sendUserRegistrationEmail,
  sendProviderStatusChangeEmail,
};