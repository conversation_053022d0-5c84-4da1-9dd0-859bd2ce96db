const express = require('express');
const authRoute = require('./auth.route');
const adminRoute = require('./admin.route');
const commonRoute = require('./common.route');
const customerRoute = require('./customer.route');
const providerRoute = require('./provider.route');


const docsRoute = require('./docs.route');
const config = require('../../config/config');

const router = express.Router();

const defaultRoutes = [
  {
    path: '/auth',
    route: authRoute,
  },
  {
    path: '/common',
    route: commonRoute,
  },
  {
    path: '/customer',
    route: customerRoute,
  },
  {
    path: '/provider',
    route: providerRoute,
  },
  {
    path: '/-admin',
    route: adminRoute,
  }
  

];

const devRoutes = [
  // routes available only in development mode
  {
    path: '/docs',
    route: docsRoute,
  },
];

defaultRoutes.forEach((route) => {
  router.use(route.path, route.route);
});

/* istanbul ignore next */
if (config.env === 'development') {
  devRoutes.forEach((route) => {
    router.use(route.path, route.route);
  });
}

module.exports = router;
