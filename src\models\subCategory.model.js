const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const MongooseDelete = require('mongoose-delete');

const subCategorySchema = mongoose.Schema(
  {
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category',
      required: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    status: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);
// Adding a compound index for unique category-name combination
subCategorySchema.index({ category: 1, name: 1 }, { unique: true });
subCategorySchema.plugin(toJSON);
subCategorySchema.plugin(paginate);
subCategorySchema.plugin(MongooseDelete, { overrideMethods: 'all' });

/**
 * @typedef SubCategory
 */
const SubCategory = mongoose.model('subCategory', subCategorySchema);
module.exports = SubCategory;
