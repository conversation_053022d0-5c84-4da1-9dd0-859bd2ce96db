const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const MongooseDelete = require('mongoose-delete');
const { required } = require('joi');

const reviewSchema = mongoose.Schema(
  {
    order_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Orders',
      required: true,
    },
    
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5,
    },
    title: {
      type: String,
      required: true,
      trim: true,
    },
    experience: {
      type: String,
      trim: true,
    },
    image: {
      type:String,
      required:false,
    }
  },
  {
    timestamps: true,
  }
);

// Plugins
reviewSchema.plugin(toJSON);
reviewSchema.plugin(MongooseDelete, { overrideMethods: 'all' });
reviewSchema.plugin(paginate);

const Reviews = mongoose.model('Reviews', reviewSchema);

module.exports = Reviews;
