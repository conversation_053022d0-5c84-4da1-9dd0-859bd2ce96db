const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const MongooseDelete = require('mongoose-delete');

const variantSchema = mongoose.Schema(
  {
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true,
    },
    amount: {
      type: Number,
      required: true,
      default: 0,
    },
    unit: {
      type: String,
      required: true,
      required: ['Gram', 'Kilogram', 'Milliliter', 'Liter', 'Piece'],
    },
    default: {
      type: Boolean,
      default: true,
    },
    fake_price: {
      type: Number,
      required: true,
      default: 0,
    },
    price: {
      type: Number,
      required: true,
      default: 0,
    },
    flavour: {
      type: String,
      required: false,
      trim: true,
    },
    stock: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
  },
  {
    timestamps: true,
  }
);

// Add plugin that converts mongoose to json
variantSchema.plugin(toJSON);
variantSchema.plugin(paginate);
variantSchema.plugin(MongooseDelete, { overrideMethods: 'all' });


/**
 * @typedef variant
 */
const variant = mongoose.model('variant', variantSchema);
module.exports = variant;
