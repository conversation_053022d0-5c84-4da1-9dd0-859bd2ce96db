const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const unitSchema = mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    status: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

unitSchema.plugin(toJSON);
unitSchema.plugin(paginate);

const unit = mongoose.model('unit', unitSchema);

module.exports = unit;