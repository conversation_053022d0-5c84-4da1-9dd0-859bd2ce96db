const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const MongooseDelete = require('mongoose-delete');

const optionSchema = mongoose.Schema(
  {
    question: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Question', // Reference to the Question model
      required: true,
    },
    value: {
      type: String,
      required: true, // The value associated with the option
      trim: true,
    },
  },
  // {
  //   timestamps: true,
  // }
);

// Add a compound index for question and value to ensure uniqueness
optionSchema.index({ question: 1, value: 1 }, { unique: true });

// Add plugin that converts mongoose to json
optionSchema.plugin(toJSON);
optionSchema.plugin(paginate);
optionSchema.plugin(MongooseDelete, { overrideMethods: 'all' });

const Option = mongoose.model('Option', optionSchema);
module.exports = Option;
