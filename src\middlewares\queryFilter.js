const pick = require('../utils/pick');

/**
 * Middleware to process and standardize query parameters for filtering, searching, and sorting
 * Extends the existing functionality to support search across multiple fields
 * 
 * @param {Object} options - Configuration options
 * @param {Array} options.searchFields - Fields to search across (e.g., ['name', 'email', 'description'])
 * @param {Object} options.defaultSort - Default sort configuration (e.g., { createdAt: -1 })
 * @param {Array} options.allowedSortFields - Fields that can be used for sorting
 * @returns {Function} Express middleware function
 */
const queryFilter = (options = {}) => {
  const {
    searchFields = [],
    defaultSort = { createdAt: -1 },
    allowedSortFields = []
  } = options;

  return (req, res, next) => {
    // Extract standard pagination and sorting parameters
    const standardOptions = pick(req.query, ['sortBy', 'limit', 'page']);

    // Extract search parameter
    const { search, orderBy } = req.query;

    // Process search parameter - only if search is provided and searchFields are configured
    let searchFilter = {};
    if (search && search.trim() && searchFields.length > 0) {
      const searchRegex = { $regex: search.trim(), $options: 'i' };

      if (searchFields.length === 1) {
        // Single field search
        searchFilter[searchFields[0]] = searchRegex;
      } else {
        // Multiple field search using $or
        searchFilter.$or = searchFields.map(field => ({
          [field]: searchRegex
        }));
      }
    }

    // Process orderBy parameter (alternative to sortBy for more intuitive usage)
    if (orderBy && !standardOptions.sortBy) {
      // Parse orderBy format: "field:asc" or "field:desc" or just "field" (defaults to asc)
      const orderParts = orderBy.split(':');
      const field = orderParts[0];
      const direction = orderParts[1] || 'asc';

      // Validate field if allowedSortFields is specified
      if (allowedSortFields.length === 0 || allowedSortFields.includes(field)) {
        standardOptions.sortBy = `${field}:${direction}`;
      }
    }

    // Attach processed options to request object
    req.queryOptions = standardOptions;
    // Only set searchFilter if it has content, otherwise leave it undefined for truly optional search
    const hasSearchContent = searchFilter && typeof searchFilter === 'object' && Object.keys(searchFilter).length > 0;
    req.searchFilter = hasSearchContent ? searchFilter : undefined;
    req.hasSearch = hasSearchContent;

    // For backward compatibility, also set the original options
    req.options = standardOptions;

    next();
  };
};

/**
 * Helper function to create search aggregation pipeline
 * @param {Object} searchFilter - MongoDB filter object for search
 * @param {Array} baseAggregation - Base aggregation pipeline
 * @returns {Array} Modified aggregation pipeline with search filter
 */
const addSearchToAggregation = (searchFilter, baseAggregation = []) => {
  // Handle undefined, null, or empty searchFilter
  if (!searchFilter || typeof searchFilter !== 'object' || Object.keys(searchFilter).length === 0) {
    return baseAggregation;
  }

  // Add search filter at the beginning of aggregation pipeline
  // This ensures search is applied before any lookups or transformations
  return [
    { $match: searchFilter },
    ...baseAggregation
  ];
};

/**
 * Helper function to get searchable fields for different models
 * This can be extended as needed for different models
 */
const getSearchFields = (modelName) => {
  const searchFieldsMap = {
    'Category': ['name', 'description'],
    'Question': ['value'],
    'Customer': ['name', 'email'],
    'Provider': ['name', 'email', 'description'],
    'Product': ['name', 'description'],
    'Order': ['status'],
    'Review': ['title', 'experience'],
    'Notification': ['title', 'message']
  };
  
  return searchFieldsMap[modelName] || [];
};

/**
 * Helper function to get allowed sort fields for different models
 */
const getAllowedSortFields = (modelName) => {
  const sortFieldsMap = {
    'Category': ['name', 'createdAt', 'updatedAt'],
    'Question': ['value', 'sequence', 'createdAt', 'updatedAt'],
    'Customer': ['name', 'email', 'createdAt', 'updatedAt'],
    'Provider': ['name', 'email', 'createdAt', 'updatedAt', 'isApproved'],
    'Product': ['name', 'price', 'createdAt', 'updatedAt'],
    'Order': ['status', 'createdAt', 'updatedAt', 'price'],
    'Review': ['rating', 'createdAt', 'updatedAt'],
    'Notification': ['createdAt', 'updatedAt', 'isRead']
  };
  
  return sortFieldsMap[modelName] || ['createdAt', 'updatedAt'];
};

/**
 * Convenience function to create model-specific query filter middleware
 */
const createModelQueryFilter = (modelName) => {
  return queryFilter({
    searchFields: getSearchFields(modelName),
    allowedSortFields: getAllowedSortFields(modelName),
    defaultSort: { createdAt: -1 }
  });
};

module.exports = {
  queryFilter,
  addSearchToAggregation,
  getSearchFields,
  getAllowedSortFields,
  createModelQueryFilter
};
